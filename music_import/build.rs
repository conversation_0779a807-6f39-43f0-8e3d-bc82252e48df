fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 检查 protoc 是否可用
    if let Err(e) = std::process::Command::new("protoc").arg("--version").output() {
        eprintln!("Warning: protoc not found: {}", e);
        eprintln!("Please install Protocol Buffers compiler:");
        eprintln!("  - Ubuntu/Debian: sudo apt-get install protobuf-compiler");
        eprintln!("  - macOS: brew install protobuf");
        eprintln!("  - Windows: choco install protoc");
        eprintln!("  - Or download from: https://github.com/protocolbuffers/protobuf/releases");
    }

    // 设置重新构建条件
    println!("cargo:rerun-if-changed=proto/file_transfer.proto");
    println!("cargo:rerun-if-changed=proto");

    // 编译 protobuf 文件
    tonic_build::compile_protos("proto/file_transfer.proto")?;

    Ok(())
}