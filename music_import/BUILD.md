# 音乐导入系统 - 编译指南

## 🎯 支持的平台

- ✅ **Windows** (x86_64)
- ✅ **macOS** (x86_64, ARM64)
- ✅ **Linux** (x86_64)

## 🔧 编译要求

### 通用要求
- **Rust** 1.70+ (推荐使用最新稳定版)
- **Git** (用于克隆代码)

### Windows 特定要求
- **Visual Studio Build Tools** 或 **Visual Studio Community**
- **Windows SDK**

### macOS 特定要求
- **Xcode Command Line Tools**

### Linux 特定要求
- **build-essential** (Ubuntu/Debian)
- **gcc** 和 **pkg-config**

## 🚀 快速编译

### 1. 克隆代码
```bash
git clone <repository-url>
cd music_import
```

### 2. 安装 Rust (如果未安装)
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

### 3. 编译 Release 版本
```bash
cargo build --release
```

编译完成后，二进制文件位于：
- **Windows**: `target/release/music_import.exe`
- **macOS/Linux**: `target/release/music_import`

## 🌐 交叉编译

### Windows 上编译其他平台

#### 编译 Linux 版本
```bash
# 安装目标
rustup target add x86_64-unknown-linux-gnu

# 安装交叉编译工具 (需要 WSL 或 Docker)
# 使用 Docker 编译 (推荐)
docker run --rm -v ${PWD}:/workspace -w /workspace rust:latest cargo build --release --target x86_64-unknown-linux-gnu
```

### macOS 上编译其他平台

#### 编译 Linux 版本
```bash
# 安装目标
rustup target add x86_64-unknown-linux-gnu

# 安装交叉编译工具
brew install SergioBenitez/osxct/x86_64-unknown-linux-gnu

# 编译
cargo build --release --target x86_64-unknown-linux-gnu
```

#### 编译 Windows 版本 (复杂)
```bash
# 注意: Windows 交叉编译在 macOS 上比较复杂
# 推荐使用 GitHub Actions 或在 Windows 机器上编译
```

### Linux 上编译其他平台

#### 编译 Windows 版本
```bash
# 安装目标
rustup target add x86_64-pc-windows-gnu

# 安装 MinGW
sudo apt install mingw-w64

# 编译
cargo build --release --target x86_64-pc-windows-gnu
```

## 🤖 自动化编译 (推荐)

### 使用 GitHub Actions

1. 将代码推送到 GitHub
2. GitHub Actions 会自动为所有平台编译
3. 在 Releases 页面下载编译好的二进制文件

### 使用 Docker

```bash
# 创建多平台编译容器
docker buildx create --use

# 编译所有平台
docker buildx build --platform linux/amd64,linux/arm64 -t music_import .
```

## 📦 发布包结构

编译完成的发布包应包含：

```
music_import-{platform}/
├── music_import(.exe)           # 主程序
├── config/                      # 配置文件
│   ├── server.json
│   ├── sony.json
│   ├── warner.json
│   └── umg.json
├── test_scripts/                # 测试脚本
│   ├── test_server.sh/.bat
│   ├── test_client.sh/.bat
│   └── README.md
├── README.md                    # 使用说明
└── BUILD.md                     # 编译说明
```

## 🐛 常见编译问题

### Windows

**问题**: `error: Microsoft Visual C++ 14.0 is required`
**解决**: 安装 Visual Studio Build Tools

**问题**: `error: failed to run custom build command for 'openssl-sys'`
**解决**: 
```bash
# 使用 vcpkg 安装 OpenSSL
vcpkg install openssl:x64-windows
set VCPKG_ROOT=C:\vcpkg
```

### macOS

**问题**: `error: failed to run custom build command for 'ring'`
**解决**: 
```bash
xcode-select --install
```

### Linux

**问题**: `error: failed to run custom build command for 'openssl-sys'`
**解决**: 
```bash
# Ubuntu/Debian
sudo apt install libssl-dev pkg-config

# CentOS/RHEL
sudo yum install openssl-devel pkgconfig
```

## 🎯 性能优化

### 编译优化
```bash
# 最大优化 (编译时间更长，但性能更好)
RUSTFLAGS="-C target-cpu=native" cargo build --release

# 减小二进制大小
cargo build --release --config profile.release.strip=true
```

### 链接时优化 (LTO)
在 `Cargo.toml` 中添加：
```toml
[profile.release]
lto = true
codegen-units = 1
panic = "abort"
```

## 📈 编译统计

- **编译时间**: 约 2-5 分钟 (取决于硬件)
- **二进制大小**: 约 8-15MB (取决于平台)
- **依赖数量**: 约 400 个 crate
- **内存需求**: 编译时需要 2-4GB RAM

## 🔄 持续集成

项目配置了 GitHub Actions，每次推送代码时会自动：
1. 编译所有支持的平台
2. 运行测试
3. 创建发布包
4. 上传到 Releases

这确保了所有平台的二进制文件都是最新且经过测试的。
