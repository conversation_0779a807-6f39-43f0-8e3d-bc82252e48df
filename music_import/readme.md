# 音乐导入工具

该工具用于将索尼、华纳和 UMG 的音乐数据导入 NAS 系统。它包含一个客户端和一个服务器组件。

## 开发规范与流程

### 核心设计原则

1. **高内聚，低耦合 (High Cohesion, Low Coupling)**
   - 模块内部功能紧密相关，模块间依赖最小化
   - 每个模块专注于单一职责

2. **关注点分离 (Separation of Concerns, SoC)**
   - 配置管理、业务逻辑、数据访问分离
   - DDEX XML 解析属于业务逻辑，不在配置文件范围内

3. **面向接口设计 (Design to an Interface)**
   - 定义清晰的接口契约
   - 依赖抽象而非具体实现

4. **可测试性设计 (Design for Testability)**
   - 支持单元测试和集成测试
   - 依赖注入，便于 Mock 测试

5. **简洁与可演化性 (Simplicity and Evolvability)**
   - 保持代码简洁易懂
   - 支持功能扩展和维护

### 配置文件规范

- **统一使用 JSON 格式**：所有程序配置文件采用 JSON 格式
- **业务数据分离**：DDEX XML 解析等业务行为不属于配置范围
- **环境配置统一**：开发、测试、生产环境配置统一管理，由开发人员自行维护

## 工作流程

### 服务器端

服务器负责接收和存储由客户端上传的文件。

```mermaid
graph TD
    A[启动服务器] --> B{读取配置文件};
    B --> C[启动 gRPC 服务];
    C --> D{监听文件上传请求}; 
    D -- 收到请求 --> E[接收文件数据流];
    E --> F[将文件保存到上传目录];
    F --> D;
```

### 客户端（含智能数据对比功能）

客户端负责处理特定提供商的数据，解析元数据，并通过 gRPC 将媒体文件上传到服务器。

```mermaid
graph TD
    A[启动客户端] --> B[读取 JSON 配置文件];
    B --> C[从配置获取提供商和目录信息];
    C --> D[根据提供商初始化导入器];
    D --> E[初始化 gRPC 客户端可选];
    E --> F[初始化解析状态跟踪器];
    F --> G[扫描配置目录下所有 XML 文件];
    G --> H{检查文件解析状态};
    H -- 已解析成功 --> I[跳过文件];
    H -- 未解析或失败 --> J[标记开始解析];
    J --> K[解析 DDEX XML 文件];
    K --> L{解析成功?};
    L -- 失败 --> N[标记解析失败];
    L -- 成功 --> M[标记解析成功];
    M --> O[上传加密音频和图片资源到 NAS];
    O --> P[开始智能数据处理];
    P --> P1{智能对比模式?};
    P1 -- 启用 --> P2[对比专辑数据];
    P1 -- 禁用 --> P6[传统模式直接插入];
    P2 --> P3{专辑数据变化?};
    P3 -- 新专辑 --> P4[插入新专辑];
    P3 -- 无变化 --> P5[跳过专辑更新];
    P3 -- 有变化 --> P7[更新专辑数据];
    P4 --> P8[对比曲目数据];
    P5 --> P8;
    P7 --> P8;
    P8 --> P9{曲目数据变化?};
    P9 -- 新曲目 --> P10[插入新曲目];
    P9 -- 无变化 --> P11[跳过曲目更新];
    P9 -- 有变化 --> P12[更新曲目数据];
    P10 --> P13[建立关联关系];
    P11 --> P13;
    P12 --> P13;
    P6 --> P13;
    P13 --> P14[生成处理统计];
    P14 --> Q[记录处理日志每日格式];
    Q --> R[继续处理下一个文件];
    N --> Q;
    I --> R;
    R --> S{还有文件?};
    S -- 是 --> H;
    S -- 否 --> T[生成统计报告];
    T --> U[发送邮件报告可选];
    U --> V[结束];
```

## 智能数据对比功能

### 功能特点

- **智能检测**：基于 GRid（专辑）和 ISRC（曲目）检测重复数据
- **字段级对比**：精确检测哪些字段发生了变化
- **处理策略**：新记录直接插入，无变化跳过处理，有变化只更新变化字段
- **性能优化**：避免不必要的数据库操作，提高导入效率

### 简化对比模式

通过配置文件中的 `check_before_upload` 字段控制：
- `true`：启用上传前检查模式（先检查 ISRC/ICPN，存在则跳过上传）
- `false`：使用标准模式（先上传文件，然后进行智能对比）

**注意**: 所有数据库操作都会进行智能对比和更新，不再有传统的直接插入模式。

## 使用方法

### 环境变量设置

使用新版加密 (v1) 时，需要设置主密钥环境变量：

```bash
# Linux/macOS
export AUDIO_MASTER_KEY="your_32_byte_hex_key_here"

# Windows
set AUDIO_MASTER_KEY=your_32_byte_hex_key_here
```

**生成主密钥示例**：
```bash
# 生成 32 字节随机密钥
openssl rand -hex 32
```

### 启动 NAS 服务器

```bash
cargo run --bin music_import -- --mode server --config config/server.json
```

### 运行客户端

#### 使用老版加密（兼容模式）
```bash
cargo run --bin music_import -- --mode client --config config/sony.json
```

#### 使用新版加密（高安全性）
```bash
# 设置环境变量
export AUDIO_MASTER_KEY="your_32_byte_hex_key_here"

# 启动客户端
 确认 `config/sony.json` 中的 `encryption` 已设为 v1/aes_gcm
cargo run --bin music_import -- --mode client --config config/sony_v1.json
```

#### 多厂商支持
```bash
# Sony 数据导入
cargo run --bin music_import -- --mode client --config config/sony.json

# UMG 数据导入
cargo run --bin music_import -- --mode client --config config/umg.json

# Warner 数据导入
cargo run --bin music_import -- --mode client --config config/warner.json
```

### 测试 gRPC

```bash
nc -zv ************ 50051

netstat -an | findstr :50051
```

---
*   **mode**: `server` 或 `client`
*   **config**: 配置文件的路径 (JSON 格式)

**注意**:
- 提供商信息和数据目录路径已在 JSON 配置文件中指定，程序会自动扫描配置的 `root_folder` 目录下的所有文件进行解析
- 使用新版加密时必须设置 `AUDIO_MASTER_KEY` 环境变量
- 不同加密版本生成的文件不兼容，请根据需求选择合适的配置

## 加密配置说明

### 支持的加密方式

项目支持两种加密方式，以满足不同的兼容性和安全性需求：

| 对比项目 | 老版加密 (Legacy) | 新版加密 (V1) |
|---------|------------------|---------------|
| **版本标识** | `legacy` | `v1` |
| **加密算法** | 按位取反 (~byte) | AES-256-GCM |
| **文件扩展名** | `.bin` | `.encrypted` |
| **安全级别** | ⚠️ 低 (可逆转换) | 🔒 高 (军用级加密) |
| **性能表现** | 🚀 极快 | ⚡ 快 |
| **文件大小** | 📦 无增长 | 📦 +16字节 (认证标签) |
| **兼容性** | ✅ 完全兼容 .NET 项目 | ❌ 仅限 Rust 项目 |
| **随机访问** | ✅ 支持范围解密 | ❌ 需要完整解密 |
| **密钥管理** | 🔓 无需密钥 | 🔑 需要 32 字节主密钥 |
| **数据完整性** | ❌ 无校验 | ✅ 内置认证标签 |
| **抗攻击能力** | ❌ 极易破解 | ✅ 抗量子计算攻击 |
| **适用场景** | 兼容性优先 | 安全性优先 |

## 加密方式选择建议

### 🔧 选择老版加密 (`legacy`) 当：
- ✅ **兼容性要求**：需要与原始 .NET 项目完全兼容
- ✅ **性能优先**：对处理速度要求极高（大文件批量处理）
- ✅ **随机访问**：需要支持文件范围解密功能
- ✅ **简单部署**：不想管理加密密钥
- ⚠️ **注意**：仅适用于内网环境，不建议用于敏感数据

### 🔒 选择新版加密 (`v1`) 当：
- ✅ **安全优先**：对数据安全有较高要求
- ✅ **新系统部署**：全新部署的系统（强烈推荐）
- ✅ **数据完整性**：需要确保数据未被篡改
- ✅ **合规要求**：满足企业安全合规标准
- ✅ **未来兼容**：为系统长期发展考虑

## 配置文件示例

### 客户端配置 - 老版加密 (config/sony.json)

```json
{
  "provider": "sony",
  "app_setting": {
    "thread_count": 4,
    "need_email": false,
    "force_update": false,
    "smart_comparison": true
  },
  "source": {
    "root_folder": "./data/sony",
    "log_folder": "./logs"
  },
  "database": {
    "host": "*************",
    "user": "new_avs",
    "password": "aZb2CQQwF8zS83nE",
    "database": "avs",
    "port": 3336
  },
  "grpc": {
    "server_addr": "http://************:50051",
    "timeout_seconds": 30
  },
  "mail": {
    "smtp_server": "smtp.example.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "password",
    "from_email": "<EMAIL>",
    "to_emails": ["<EMAIL>"]
  },
  "encryption": {
    "version": "legacy",
    "method": "bitwise_not"
  }
}
```

### 客户端配置 - 新版加密 (config/sony_v1.json)

```json
{
  "provider": "sony",
  "app_setting": {
    "thread_count": 4,
    "need_email": false,
    "force_update": false,
    "smart_comparison": true
  },
  "source": {
    "root_folder": "./data/sony",
    "log_folder": "./logs"
  },
  "database": {
    "host": "*************",
    "user": "new_avs",
    "password": "aZb2CQQwF8zS83nE",
    "database": "avs",
    "port": 3336
  },
  "grpc": {
    "server_addr": "http://************:50051",
    "timeout_seconds": 30
  },
  "mail": {
    "smtp_server": "smtp.example.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "password",
    "from_email": "<EMAIL>",
    "to_emails": ["<EMAIL>"]
  },
  "encryption": {
    "version": "v1",
    "method": "aes_gcm"
  }
}
```

### 服务器配置 (config/server.json)

```json
{
  "server": {
    "host": "0.0.0.0",
    "port": 50051,
    "upload_dir": "./uploads"
  },
  "database": {
    "host": "localhost",
    "user": "music_user",
    "password": "password",
    "database": "music_import",
    "port": 3306
  }
}
```

## 运行效果示例

### 智能对比模式输出

```
💾 开始智能数据处理...
🔍 步骤1: 对比专辑数据...
⏭️  专辑数据无变化: Best Album (ID: 12345)
👨‍🎤 步骤2: 处理艺术家信息...
🎵 步骤3: 对比曲目数据...
✅ 新增曲目: New Song (ID: 67890, ISRC: USRC17607839)
⏭️  曲目数据无变化: Existing Song (ID: 67891)
🔄 更新曲目: Updated Song (ID: 67892), 变化: ["trackname: 'Old Name' -> 'New Name'"]

📊 数据处理统计:
  专辑: 新增 0, 更新 0, 无变化 1
  曲目: 新增 1, 更新 1, 无变化 1
```

### 文件上传状态

```
🎧 准备上传音频文件: A1 (本地: ./data/sony/file.mp3)
⏭️  音频文件已存在，跳过上传: ./data/sony/file.mp3 -> audio/A1.encrypted
✅ 音频文件上传成功: ./data/sony/new_file.mp3 -> audio/A2.encrypted
```

## 快速参考

### 加密配置快速选择

```bash
# 🔧 老版加密 - 兼容性优先（适用于 .NET 项目迁移）
"encryption": {
  "version": "legacy",
  "method": "bitwise_not"
}
# 特点：极快速度，完全兼容，无密钥管理，但安全性较低

# 🔒 新版加密 - 安全性优先（推荐用于新系统）
"encryption": {
  "version": "v1",
  "method": "aes_gcm"
}
# 特点：军用级安全，数据完整性校验，需要密钥管理
```

### 环境变量设置对比

```bash
# 老版加密 - 无需设置环境变量
# 直接运行即可

# 新版加密 - 必须设置主密钥
export AUDIO_MASTER_KEY="your_32_byte_hex_key_here"
# 生成密钥: openssl rand -hex 32
```

### 技术实现对比

| 技术细节 | 老版加密 (Legacy) | 新版加密 (V1) |
|---------|------------------|---------------|
| **加密原理** | 每字节按位取反 `~byte` | AES-256-GCM 对称加密 |
| **密钥长度** | 无密钥 | 256 位 (32 字节) |
| **初始化向量** | 无 | 96 位随机 IV |
| **认证标签** | 无 | 128 位 GMAC 标签 |
| **加密速度** | ~2GB/s | ~500MB/s |
| **解密速度** | ~2GB/s | ~500MB/s |
| **内存占用** | 极低 | 中等 |
| **CPU 占用** | 极低 | 中等 |
| **文件头信息** | 无 | 版本标识 + IV + 标签 |
| **错误检测** | 无 | 自动检测篡改 |
| **并发安全** | ✅ 线程安全 | ✅ 线程安全 |

### 安全性分析

| 安全维度 | 老版加密 | 新版加密 |
|---------|---------|---------|
| **机密性** | ❌ 极低 (简单可逆) | ✅ 极高 (AES-256) |
| **完整性** | ❌ 无保护 | ✅ GMAC 认证 |
| **可用性** | ✅ 高 (简单快速) | ✅ 高 (可靠稳定) |
| **抗暴力破解** | ❌ 瞬间破解 | ✅ 2^256 复杂度 |
| **抗已知明文攻击** | ❌ 完全暴露 | ✅ 强抗性 |
| **抗差分攻击** | ❌ 无抗性 | ✅ 强抗性 |
| **前向安全性** | ❌ 无 | ✅ 每次随机 IV |




多NAS架构
flowchart TD
    subgraph "数据上传层"
        A[DDEX解析程序<br/>Master Key #1]
    end
    
    subgraph "NAS存储层"
        B[Sony NAS<br/>Master Key #2]
        C[Warner NAS<br/>Master Key #3] 
        D[UMG NAS<br/>Master Key #4]
    end
    
    subgraph "业务应用层"
        E[PHP应用1 → Sony]
        F[PHP应用2 → Warner]
        G[PHP应用3 → UMG]
    end
    
    subgraph "用户访问层"
        H[最终用户]
    end
    
    A -->|加密上传| B
    A -->|加密上传| C
    A -->|加密上传| D
    
    E -->|HTTP请求| B
    F -->|HTTP请求| C
    G -->|HTTP请求| D
    
    B -->|解密数据| E
    C -->|解密数据| F
    D -->|解密数据| G
    
    E --> H
    F --> H
    G --> H
    
    style A fill:#ff5722,color:#fff
    style B fill:#ff5722,color:#fff
    style C fill:#ff5722,color:#fff
    style D fill:#ff5722,color:#fff




密钥管理中心架构
flowchart TD
    subgraph "密钥管理中心"
        KMS[密钥管理服务<br/>Master Key ONLY]
    end
    
    subgraph "数据上传层"
        A[DDEX解析程序<br/>无密钥]
    end
    
    subgraph "NAS存储层"
        B[Sony NAS<br/>无密钥]
        C[Warner NAS<br/>无密钥]
        D[UMG NAS<br/>无密钥]
    end
    
    subgraph "业务应用层"
        E[PHP应用]
    end
    
    A -->|请求加密| KMS
    KMS -->|返回加密数据| A
    A -->|上传加密文件| B
    A -->|上传加密文件| C
    A -->|上传加密文件| D
    
    E -->|请求解密| KMS
    KMS -->|查询NAS获取加密数据| B
    KMS -->|查询NAS获取加密数据| C  
    KMS -->|查询NAS获取加密数据| D
    KMS -->|返回解密数据| E
    
    style KMS fill:#4caf50,color:#fff
    style A fill:#2196f3,color:#fff
    style B fill:#2196f3,color:#fff
    style C fill:#2196f3,color:#fff
    style D fill:#2196f3,color:#fff




### 与主流平台安全特性对比

| 安全特性 | 当前项目 (V1) | 当前项目 (Legacy) | Spotify | Apple Music | Netflix |
|---------|---------------|------------------|---------|-------------|---------|
| **传输加密** | ✅ TLS 1.3 | ✅ TLS 1.3 | ✅ TLS | ✅ TLS | ✅ TLS |
| **存储加密** | ✅ AES-256-GCM | ⚠️ 按位取反 | ✅ AES-256 | ✅ AES-256 | ✅ AES-256 |
| **数据完整性** | ✅ GMAC 认证 | ❌ 无保护 | ✅ HMAC | ✅ 数字签名 | ✅ 校验和 |
| **DRM 保护** | ❌ 无 | ❌ 无 | ✅ 自定义 | ✅ FairPlay | ✅ Widevine |
| **密钥管理** | 🟡 环境变量 | ✅ 无需密钥 | ✅ HSM | ✅ Secure Enclave | ✅ 云端 KMS |
| **密钥轮换** | 🟡 手动 | ✅ 无需轮换 | ✅ 自动 | ✅ 自动 | ✅ 自动 |
| **访问控制** | 🟡 基础 | 🟡 基础 | ✅ 完整 | ✅ 完整 | ✅ 完整 |
| **审计日志** | ✅ 详细 | ✅ 详细 | ✅ 详细 | ✅ 详细 | ✅ 详细 |
| **合规认证** | 🟡 部分 | ❌ 不适用 | ✅ 多项认证 | ✅ 多项认证 | ✅ 多项认证 |

### 加密方案适用场景总结

#### 🏢 企业内网环境
- **老版加密**: ✅ 适用 (性能优先，内网相对安全)
- **新版加密**: ✅ 更佳 (即使内网也建议高安全)

#### 🌐 公网传输
- **老版加密**: ❌ 不建议 (安全风险极高)
- **新版加密**: ✅ 必须 (符合安全标准)

#### 📊 大数据处理
- **老版加密**: ✅ 适用 (极高性能需求)
- **新版加密**: 🟡 可用 (性能略降但可接受)

#### 🔒 敏感数据存储
- **老版加密**: ❌ 禁用 (无法保证安全)
- **新版加密**: ✅ 必须 (唯一安全选择)