//! 统一导入器
//! 
//! 使用新的 ImportEngine 架构，支持所有音乐提供商

use anyhow::Result;
use log::{info, error};
use std::collections::HashMap;

use crate::config::Config;
use crate::importers::engine::{ImportEngine, ImportResult};
use crate::importers::{Importer, ImportResult as LegacyImportResult};

/// 统一导入器
/// 
/// 使用新的 ImportEngine 架构，替代原有的 Sony/Warner/UMG 导入器
pub struct UnifiedImporter {
    engine: ImportEngine,
    provider: String,
}

impl UnifiedImporter {
    /// 创建新的统一导入器
    pub fn new(config: Config, date: String, provider: &str) -> Result<Self> {
        info!("🚀 创建统一导入器 - 提供商: {}", provider);
        
        let engine = ImportEngine::new(config, date, provider)?;
        
        Ok(Self {
            engine,
            provider: provider.to_string(),
        })
    }
    
    /// 初始化导入器
    pub async fn initialize(&mut self) -> Result<()> {
        info!("🔧 初始化统一导入器 - {}", self.provider);
        self.engine.initialize().await
    }
    
    /// 开始导入流程
    pub async fn start_import_async(&mut self) -> Result<()> {
        info!("🚀 开始 {} 导入流程", self.provider);

        // 显示当前配置模式
        let config = self.engine.get_config();
        if config.check_before_upload {
            info!("📋 启用上传前检查模式（先检查 ISRC/ICPN，存在则跳过上传）");
        } else {
            info!("📋 使用标准模式（先上传文件，然后智能对比）");
        }

        match self.engine.start_import().await {
            ImportResult::Success => {
                info!("✅ {} 导入流程完成", self.provider);
                Ok(())
            }
            ImportResult::Failure(error) => {
                error!("❌ {} 导入流程失败: {}", self.provider, error);
                Err(anyhow::anyhow!("Import failed: {}", error))
            }
        }
    }
    
    /// 获取处理记录
    pub fn get_records(&self) -> HashMap<String, Vec<String>> {
        self.engine.get_records()
    }

    /// 发送邮件报告
    pub fn send_email_report(&self) -> Result<()> {
        self.engine.send_email_report()
    }
    
    /// 清理资源
    pub fn cleanup(&mut self) {
        self.engine.cleanup();
    }
}

impl Drop for UnifiedImporter {
    fn drop(&mut self) {
        self.cleanup();
    }
}

// 为了向后兼容，实现原有的 Importer trait
impl Importer for UnifiedImporter {
    fn start_import(&mut self) -> LegacyImportResult {
        // 使用 tokio 的 block_on 来在同步上下文中运行异步代码
        let rt = tokio::runtime::Runtime::new().unwrap();
        match rt.block_on(self.start_import_async()) {
            Ok(()) => LegacyImportResult::Success,
            Err(e) => LegacyImportResult::Failure(e.to_string()),
        }
    }

    fn get_records(&self) -> HashMap<String, Vec<String>> {
        UnifiedImporter::get_records(self)
    }

    fn send_email(&self) -> Result<()> {
        self.send_email_report()
    }

    fn cleanup(&mut self) {
        self.cleanup();
    }
}

/// 创建 Sony 导入器（向后兼容）
pub fn create_sony_importer(config: Config, date: String) -> Result<UnifiedImporter> {
    UnifiedImporter::new(config, date, "sony")
}

/// 创建 Warner 导入器（向后兼容）
pub fn create_warner_importer(config: Config, date: String) -> Result<UnifiedImporter> {
    UnifiedImporter::new(config, date, "warner")
}

/// 创建 UMG 导入器（向后兼容）
pub fn create_umg_importer(config: Config, date: String) -> Result<UnifiedImporter> {
    UnifiedImporter::new(config, date, "umg")
}

/// 导入器工厂
pub struct ImporterFactory;

impl ImporterFactory {
    /// 创建导入器
    pub fn create_importer(provider: &str, config: Config, date: String) -> Result<UnifiedImporter> {
        match provider.to_lowercase().as_str() {
            "sony" => create_sony_importer(config, date),
            "warner" => create_warner_importer(config, date),
            "umg" => create_umg_importer(config, date),
            _ => Err(anyhow::anyhow!("Unsupported provider: {}", provider)),
        }
    }
    
    /// 获取所有支持的提供商
    pub fn get_supported_providers() -> Vec<&'static str> {
        vec!["sony", "warner", "umg"]
    }
    
    /// 检查是否支持该提供商
    pub fn is_supported_provider(provider: &str) -> bool {
        Self::get_supported_providers()
            .iter()
            .any(|&p| p.eq_ignore_ascii_case(provider))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_importer_factory() {
        assert!(ImporterFactory::is_supported_provider("sony"));
        assert!(ImporterFactory::is_supported_provider("WARNER"));
        assert!(ImporterFactory::is_supported_provider("umg"));
        assert!(!ImporterFactory::is_supported_provider("unknown"));
        
        let providers = ImporterFactory::get_supported_providers();
        assert_eq!(providers.len(), 3);
        assert!(providers.contains(&"sony"));
        assert!(providers.contains(&"warner"));
        assert!(providers.contains(&"umg"));
    }
    
    #[tokio::test]
    async fn test_unified_importer_creation() {
        let config = Config::default_for_testing("sony");
        let date = "20231201".to_string();
        
        let result = UnifiedImporter::new(config, date, "sony");
        assert!(result.is_ok());
        
        let importer = result.unwrap();
        assert_eq!(importer.provider, "sony");
    }
}
