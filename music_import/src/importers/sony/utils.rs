//! Sony 导入器工具函数
//! 
//! 提供各种数据转换、验证和辅助功能

use super::SonyError;
use anyhow::Result;
use log::debug;
use std::path::{Path, PathBuf};

/// 解析 DDEX 时间格式 (PT0H2M34S) 为秒数
pub fn parse_duration(duration_str: &str) -> Result<i64, SonyError> {
    if duration_str.is_empty() {
        return Ok(0);
    }
    
    debug!("解析时长: {}", duration_str);
    
    // 标准格式: PT0H2M34S 或 PT2M34S 或 PT34S
    if !duration_str.starts_with("PT") {
        return Err(SonyError::ConversionError(
            format!("无效的时长格式: {}", duration_str)
        ));
    }
    
    let duration_part = &duration_str[2..]; // 去掉 "PT"
    let mut total_seconds = 0i64;
    let mut current_number = String::new();
    
    for ch in duration_part.chars() {
        match ch {
            '0'..='9' => {
                current_number.push(ch);
            }
            'H' => {
                if !current_number.is_empty() {
                    let hours: i64 = current_number.parse()
                        .map_err(|_| SonyError::ConversionError(
                            format!("无效的小时数: {}", current_number)
                        ))?;
                    total_seconds += hours * 3600;
                    current_number.clear();
                }
            }
            'M' => {
                if !current_number.is_empty() {
                    let minutes: i64 = current_number.parse()
                        .map_err(|_| SonyError::ConversionError(
                            format!("无效的分钟数: {}", current_number)
                        ))?;
                    total_seconds += minutes * 60;
                    current_number.clear();
                }
            }
            'S' => {
                if !current_number.is_empty() {
                    let seconds: i64 = current_number.parse()
                        .map_err(|_| SonyError::ConversionError(
                            format!("无效的秒数: {}", current_number)
                        ))?;
                    total_seconds += seconds;
                    current_number.clear();
                }
            }
            _ => {
                return Err(SonyError::ConversionError(
                    format!("时长格式中包含无效字符: {}", ch)
                ));
            }
        }
    }
    
    debug!("解析时长结果: {} 秒", total_seconds);
    Ok(total_seconds)
}



/// 生成本地文件路径
pub fn generate_local_path(base_dir: &str, filename: &str) -> PathBuf {
    let sanitized_filename = sanitize_filename(filename);
    Path::new(base_dir).join(sanitized_filename)
}

/// 清理文件名，移除不安全的字符
pub fn sanitize_filename(filename: &str) -> String {
    let mut sanitized = String::new();
    
    for ch in filename.chars() {
        match ch {
            // 保留安全字符
            'a'..='z' | 'A'..='Z' | '0'..='9' | '.' | '-' | '_' => {
                sanitized.push(ch);
            }
            // 将空格和其他字符替换为下划线
            _ => {
                if !sanitized.ends_with('_') {
                    sanitized.push('_');
                }
            }
        }
    }
    
    // 确保文件名不为空且不以点开头
    if sanitized.is_empty() || sanitized.starts_with('.') {
        sanitized = format!("file_{}", sanitized);
    }
    
    sanitized
}

/// 映射艺术家角色
pub fn map_artist_roles(xml_roles: Vec<&str>) -> Vec<String> {
    xml_roles.iter().map(|role| {
        match *role {
            "MainArtist" => "主要艺术家".to_string(),
            "AssociatedPerformer" => "关联表演者".to_string(),
            "Composer" => "作曲家".to_string(),
            "Lyricist" => "作词人".to_string(),
            "Producer" => "制作人".to_string(),
            "Arranger" => "编曲".to_string(),
            "Conductor" => "指挥".to_string(),
            "Performer" => "表演者".to_string(),
            _ => role.to_string(), // 保持原始角色名
        }
    }).collect()
}

/// 标准化语言代码
pub fn normalize_language_code(lang: &str) -> String {
    match lang.to_lowercase().as_str() {
        "en" | "en-us" | "english" => "en".to_string(),
        "zh" | "zh-cn" | "chinese" => "zh".to_string(),
        "ja" | "japanese" => "ja".to_string(),
        "ko" | "korean" => "ko".to_string(),
        "pl" | "polish" => "pl".to_string(),
        _ => lang.to_lowercase(),
    }
}

/// 从日期字符串中提取年份
pub fn extract_year_from_date(date_str: &str) -> Option<i32> {
    // 支持多种日期格式: 2023-07-14, 2023/07/14, 2023
    if let Some(year_part) = date_str.split('-').next()
        .or_else(|| date_str.split('/').next())
        .or_else(|| Some(date_str)) {
        year_part.parse().ok()
    } else {
        None
    }
}

/// 验证 ISRC 格式
pub fn validate_isrc(isrc: &str) -> bool {
    // ISRC 格式: CC-XXX-YY-NNNNN (12个字符，不包括连字符)
    // 例如: PLB382300288
    if isrc.len() != 12 {
        return false;
    }
    
    // 前两个字符应该是字母（国家代码）
    let chars: Vec<char> = isrc.chars().collect();
    if !chars[0].is_ascii_alphabetic() || !chars[1].is_ascii_alphabetic() {
        return false;
    }
    
    // 后面的字符应该是字母数字
    chars[2..].iter().all(|c| c.is_ascii_alphanumeric())
}

/// 验证 GRid 格式
pub fn validate_grid(grid: &str) -> bool {
    // GRid 格式: A1XXXXXXXXXXXXXXXXX (18个字符)
    // 例如: A10301A0005097982Z
    if grid.len() != 18 {
        return false;
    }
    
    // 应该以 A1 开头
    if !grid.starts_with("A1") {
        return false;
    }
    
    // 所有字符都应该是字母数字
    grid.chars().all(|c| c.is_ascii_alphanumeric())
}

/// 验证 MD5 哈希格式
pub fn validate_md5(hash: &str) -> bool {
    // MD5 哈希应该是32个十六进制字符
    if hash.len() != 32 {
        return false;
    }
    
    hash.chars().all(|c| c.is_ascii_hexdigit())
}

/// 格式化文件大小为人类可读格式
pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size_f = size as f64;
    let mut unit_index = 0;
    
    while size_f >= 1024.0 && unit_index < UNITS.len() - 1 {
        size_f /= 1024.0;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{} {}", size, UNITS[unit_index])
    } else {
        format!("{:.2} {}", size_f, UNITS[unit_index])
    }
}

/// 计算下载进度百分比
pub fn calculate_progress(downloaded: u64, total: u64) -> f32 {
    if total == 0 {
        return 0.0;
    }
    (downloaded as f32 / total as f32) * 100.0
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_duration() {
        assert_eq!(parse_duration("PT0H2M34S").unwrap(), 154);
        assert_eq!(parse_duration("PT2M34S").unwrap(), 154);
        assert_eq!(parse_duration("PT34S").unwrap(), 34);
        assert_eq!(parse_duration("PT1H0M0S").unwrap(), 3600);
        assert_eq!(parse_duration("").unwrap(), 0);
    }



    #[test]
    fn test_validate_isrc() {
        assert!(validate_isrc("PLB382300288"));
        assert!(!validate_isrc("PLB38230028")); // 太短
        assert!(!validate_isrc("123382300288")); // 开头不是字母
    }

    #[test]
    fn test_validate_grid() {
        assert!(validate_grid("A10301A0005097982Z"));
        assert!(!validate_grid("B10301A0005097982Z")); // 不以A1开头
        assert!(!validate_grid("A10301A000509798")); // 太短
    }

    #[test]
    fn test_validate_md5() {
        assert!(validate_md5("f0c98f728328fb6b6d57865deab336fe"));
        assert!(!validate_md5("f0c98f728328fb6b6d57865deab336f")); // 太短
        assert!(!validate_md5("g0c98f728328fb6b6d57865deab336fe")); // 包含非十六进制字符
    }
}
