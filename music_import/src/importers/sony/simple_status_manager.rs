use super::simple_parse_tracker::ParseStatus;
use anyhow::{Result, Context};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use tokio::fs;
use chrono::{DateTime, Utc};
use log::{info, warn, debug};
use serde::{Deserialize, Serialize};
use walkdir::WalkDir;

/// 简化的状态管理器 - 只使用本地状态文件
pub struct SimpleStatusManager {
    /// 状态目录
    status_dir: PathBuf,
    /// 厂商名称
    provider: String,
}

/// 本地状态文件结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LocalStatus {
    /// 文件路径
    pub file_path: String,
    /// 处理状态
    pub status: ParseStatus,
    /// 文件哈希
    pub file_hash: String,
    /// 开始时间
    pub start_time: Option<DateTime<Utc>>,
    /// 结束时间
    pub end_time: Option<DateTime<Utc>>,
    /// 错误信息
    pub error_message: Option<String>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 统计信息
#[derive(Debug, Clone)]
pub struct StatusStatistics {
    pub total_files: usize,
    pub success_count: usize,
    pub failed_count: usize,
    pub in_progress_count: usize,
}

impl SimpleStatusManager {
    /// 创建新的简化状态管理器
    pub fn new(provider: &str, status_dir: &str) -> Self {
        Self {
            status_dir: PathBuf::from(status_dir),
            provider: provider.to_string(),
        }
    }
    
    /// 更新文件状态
    pub async fn update_status(&self, file_path: &str, status: ParseStatus) -> Result<()> {
        let local_status_path = self.get_local_status_path(file_path)?;
        
        // 读取现有状态或创建新状态
        let mut local_status = self.load_local_status(&local_status_path).await
            .unwrap_or_else(|| LocalStatus {
                file_path: file_path.to_string(),
                status: ParseStatus::NotStarted,
                file_hash: String::new(),
                start_time: None,
                end_time: None,
                error_message: None,
                created_at: Utc::now(),
                updated_at: Utc::now(),
            });
        
        // 更新状态和时间戳
        local_status.status = status.clone();
        local_status.updated_at = Utc::now();
        
        match status {
            ParseStatus::InProgress => {
                if local_status.start_time.is_none() {
                    local_status.start_time = Some(Utc::now());
                }
            }
            ParseStatus::Success | ParseStatus::Failed => {
                local_status.end_time = Some(Utc::now());
            }
            _ => {}
        }
        
        // 保存到本地文件
        self.save_local_status(&local_status_path, &local_status).await?;
        
        debug!("状态更新完成: {} -> {:?}", file_path, status);
        Ok(())
    }
    
    /// 获取文件状态
    pub async fn get_status(&self, file_path: &str) -> Option<ParseStatus> {
        let local_status_path = self.get_local_status_path(file_path).ok()?;
        let local_status = self.load_local_status(&local_status_path).await?;
        Some(local_status.status)
    }
    
    /// 扫描所有状态文件并返回状态映射
    pub async fn scan_all_status(&self, root_dir: &str) -> Result<HashMap<String, ParseStatus>> {
        let mut status_map = HashMap::new();
        
        info!("🔍 扫描所有状态文件...");
        let mut scanned_count = 0;
        
        // 扫描所有 XML 文件目录
        for entry in WalkDir::new(root_dir) {
            let entry = entry.context("Failed to read directory entry")?;
            
            if entry.file_type().is_file() && 
               entry.path().extension().map_or(false, |ext| ext == "xml") {
                
                let xml_path = entry.path().to_string_lossy().to_string();
                let status_path = self.get_local_status_path(&xml_path)?;
                
                if let Some(local_status) = self.load_local_status(&status_path).await {
                    status_map.insert(xml_path, local_status.status);
                    scanned_count += 1;
                }
            }
        }
        
        info!("✅ 扫描完成，找到 {} 个状态文件", scanned_count);
        Ok(status_map)
    }
    
    /// 获取统计信息
    pub async fn get_statistics(&self, root_dir: &str) -> Result<StatusStatistics> {
        let status_map = self.scan_all_status(root_dir).await?;
        
        let mut stats = StatusStatistics {
            total_files: status_map.len(),
            success_count: 0,
            failed_count: 0,
            in_progress_count: 0,
        };
        
        for status in status_map.values() {
            match status {
                ParseStatus::Success => stats.success_count += 1,
                ParseStatus::Failed => stats.failed_count += 1,
                ParseStatus::InProgress => stats.in_progress_count += 1,
                _ => {}
            }
        }
        
        Ok(stats)
    }
    
    /// 获取本地状态文件路径
    fn get_local_status_path(&self, file_path: &str) -> Result<PathBuf> {
        let path = Path::new(file_path);
        let parent = path.parent()
            .ok_or_else(|| anyhow::anyhow!("Invalid file path: {}", file_path))?;
        
        Ok(parent.join(".parse_status.json"))
    }
    
    /// 加载本地状态文件
    async fn load_local_status(&self, path: &Path) -> Option<LocalStatus> {
        match fs::read_to_string(path).await {
            Ok(content) => {
                match serde_json::from_str::<LocalStatus>(&content) {
                    Ok(status) => Some(status),
                    Err(e) => {
                        warn!("解析本地状态文件失败 {}: {}", path.display(), e);
                        None
                    }
                }
            }
            Err(_) => None,
        }
    }
    
    /// 保存本地状态文件
    async fn save_local_status(&self, path: &Path, status: &LocalStatus) -> Result<()> {
        // 确保目录存在
        if let Some(parent) = path.parent() {
            fs::create_dir_all(parent).await
                .context("Failed to create local status directory")?;
        }
        
        let content = serde_json::to_string_pretty(status)
            .context("Failed to serialize local status")?;
        
        fs::write(path, content).await
            .context("Failed to write local status file")?;
        
        Ok(())
    }
}

impl LocalStatus {
    /// 创建新的本地状态
    pub fn new(file_path: String) -> Self {
        let now = Utc::now();
        Self {
            file_path,
            status: ParseStatus::NotStarted,
            file_hash: String::new(),
            start_time: None,
            end_time: None,
            error_message: None,
            created_at: now,
            updated_at: now,
        }
    }
}
