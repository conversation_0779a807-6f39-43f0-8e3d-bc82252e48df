//! DDEX ERN XML 解析器
//! 
//! 这个模块负责解析 Sony Music 提供的 DDEX ERN 格式 XML 文件，
//! 提取专辑、曲目、艺术家和文件资源信息。

use super::*;
use anyhow::Result;
use log::{debug, info, warn};
use roxmltree::{Document, Node};
use std::collections::HashMap;

/// DDEX ERN XML 解析器
pub struct DdexParser;

impl DdexParser {
    /// 创建新的 DDEX 解析器
    pub fn new() -> Self {
        Self
    }
}

impl DdexParser {
    /// 智能查找音频录音详情节点（支持多种 DDEX 格式）
    fn find_sound_recording_details_node<'a, 'input>(sound_recording: &'a Node<'a, 'input>) -> Result<Node<'a, 'input>, SonyError> {
        // 优先尝试标准的 SoundRecordingDetailsByTerritory 格式
        if let Some(details_node) = sound_recording
            .children()
            .find(|n| n.has_tag_name("SoundRecordingDetailsByTerritory"))
        {
            debug!("找到 SoundRecordingDetailsByTerritory 节点");
            return Ok(details_node);
        }

        // 如果没有找到，尝试直接使用 SoundRecordingEdition（新格式）
        if let Some(edition_node) = sound_recording
            .children()
            .find(|n| n.has_tag_name("SoundRecordingEdition"))
        {
            debug!("使用 SoundRecordingEdition 节点作为详情节点");
            return Ok(edition_node);
        }

        Err(SonyError::XmlParseError(
            "未找到 SoundRecordingDetailsByTerritory 或 SoundRecordingEdition".to_string()
        ))
    }

    /// 智能获取标题文本（支持多种 DDEX 格式）
    fn get_title_text(node: &Node) -> Result<String, SonyError> {
        // 优先尝试 DisplayTitle/TitleText 格式
        if let Ok(title) = Self::get_nested_text_content(node, "DisplayTitle", "TitleText") {
            debug!("从 DisplayTitle/TitleText 获取标题");
            return Ok(title);
        }

        // 尝试 DisplayTitleText 格式
        if let Ok(title) = Self::get_text_content(node, "DisplayTitleText") {
            debug!("从 DisplayTitleText 获取标题");
            return Ok(title);
        }

        // 最后尝试 ReferenceTitle/TitleText 格式（向后兼容）
        if let Ok(title) = Self::get_nested_text_content(node, "ReferenceTitle", "TitleText") {
            debug!("从 ReferenceTitle/TitleText 获取标题");
            return Ok(title);
        }

        Err(SonyError::XmlParseError(
            "未找到标题：尝试了 DisplayTitle/TitleText, DisplayTitleText, ReferenceTitle/TitleText".to_string()
        ))
    }

    /// 将 URL 映射到本地文件路径
    fn map_url_to_local_path(url: &str, grid: &str, base_dir: &str) -> Result<String, SonyError> {
        // 从 URL 中提取文件名，去除查询参数
        let url_without_query = url.split('?').next().unwrap_or(url);
        let url_filename = url_without_query
            .split('/')
            .last()
            .ok_or_else(|| SonyError::XmlParseError(format!("无法从 URL 提取文件名: {}", url)))?;

        // 构建本地文件路径：base_dir/resources/grid_filename
        let local_path = format!("{}/resources/{}_{}", base_dir, grid, url_filename);

        debug!("URL 映射: {} -> {}", url, local_path);
        Ok(local_path)
    }

    /// 验证本地文件是否存在
    fn verify_local_file_exists(file_path: &str) -> bool {
        std::path::Path::new(file_path).exists()
    }

    /// 解析 XML 文件（实例方法）
    pub fn parse_xml(&self, xml_path: &str, _base_dir: &str) -> Result<super::ParsedRelease, SonyError> {
        let xml_content = std::fs::read_to_string(xml_path)
            .map_err(|e| SonyError::XmlParseError(format!("读取 XML 文件失败: {}", e)))?;

        Self::parse_xml_with_context(&xml_content, xml_path)
    }

    /// 解析 DDEX ERN XML 内容（向后兼容）
    pub fn parse_xml_static(xml_content: &str) -> Result<ParsedRelease, SonyError> {
        Self::parse_xml_with_context(xml_content, "")
    }

    /// 解析 DDEX ERN XML 内容（带上下文信息）
    pub fn parse_xml_with_context(xml_content: &str, xml_file_path: &str) -> Result<ParsedRelease, SonyError> {
        info!("开始解析 DDEX ERN XML");

        // 从文件路径推导基础目录和 GRid
        let (base_dir, grid) = if !xml_file_path.is_empty() {
            let path = std::path::Path::new(xml_file_path);
            let base_dir = path.parent()
                .and_then(|p| p.to_str())
                .unwrap_or("")
                .to_string();

            // 从文件名提取 GRid（假设文件名格式为 {grid}.xml）
            let grid = path.file_stem()
                .and_then(|s| s.to_str())
                .unwrap_or("")
                .to_string();

            debug!("推导出基础目录: {}, GRid: {}", base_dir, grid);
            (base_dir, grid)
        } else {
            (String::new(), String::new())
        };

        // 解析 XML 文档
        let doc = Document::parse(xml_content)
            .map_err(|e| SonyError::XmlParseError(format!("XML 解析失败: {}", e)))?;

        let root = doc.root_element();

        // 验证根元素
        if !root.has_tag_name("NewReleaseMessage") {
            return Err(SonyError::XmlParseError(
                "不是有效的 DDEX ERN NewReleaseMessage".to_string()
            ));
        }

        debug!("XML 根元素验证通过");

        // 解析各个部分
        let message_header = Self::parse_message_header(&root)?;
        let (audio_resources, image_resources, mut track_infos) = Self::parse_resource_list_with_context(&root, &base_dir, &grid)?;
        let album_info = Self::parse_album_info(&root)?;

        // 更新曲目序号信息
        Self::update_track_sequence_numbers(&root, &mut track_infos)?;
        
        let parsed_release = ParsedRelease {
            message_header,
            album_info,
            tracks: track_infos,
            audio_resources,
            image_resources,
        };
        
        info!("DDEX ERN XML 解析完成，专辑: {}, 曲目数: {}", 
              parsed_release.album_info.title, 
              parsed_release.tracks.len());
        
        Ok(parsed_release)
    }
    
    /// 解析消息头
    fn parse_message_header(root: &Node) -> Result<MessageHeader, SonyError> {
        debug!("解析消息头");
        
        let header_node = root
            .children()
            .find(|n| n.has_tag_name("MessageHeader"))
            .ok_or_else(|| SonyError::XmlParseError("未找到 MessageHeader".to_string()))?;
        
        let message_thread_id = Self::get_text_content(&header_node, "MessageThreadId")?;
        let message_id = Self::get_text_content(&header_node, "MessageId")?;
        let created_datetime = Self::get_text_content(&header_node, "MessageCreatedDateTime")?;
        let control_type = Self::get_text_content(&header_node, "MessageControlType")?;
        
        // 解析发送方信息
        let sender_node = header_node
            .children()
            .find(|n| n.has_tag_name("MessageSender"))
            .ok_or_else(|| SonyError::XmlParseError("未找到 MessageSender".to_string()))?;
        
        let sender = PartyInfo {
            party_id: Self::get_text_content(&sender_node, "PartyId")?,
            party_name: Self::get_nested_text_content(&sender_node, "PartyName", "FullName")?,
        };
        
        // 解析接收方信息
        let recipient_node = header_node
            .children()
            .find(|n| n.has_tag_name("MessageRecipient"))
            .ok_or_else(|| SonyError::XmlParseError("未找到 MessageRecipient".to_string()))?;
        
        let recipient = PartyInfo {
            party_id: Self::get_text_content(&recipient_node, "PartyId")?,
            party_name: Self::get_nested_text_content(&recipient_node, "PartyName", "FullName")?,
        };
        
        Ok(MessageHeader {
            message_thread_id,
            message_id,
            sender,
            recipient,
            created_datetime,
            control_type,
        })
    }
    
    /// 解析资源列表，同时收集曲目信息（向后兼容）
    fn parse_resource_list(root: &Node) -> Result<(Vec<AudioResource>, Vec<ImageResource>, Vec<TrackInfo>), SonyError> {
        Self::parse_resource_list_with_context(root, "", "")
    }

    /// 解析资源列表，同时收集曲目信息（带上下文信息）
    fn parse_resource_list_with_context(root: &Node, base_dir: &str, grid: &str) -> Result<(Vec<AudioResource>, Vec<ImageResource>, Vec<TrackInfo>), SonyError> {
        debug!("解析资源列表");

        let resource_list = root
            .children()
            .find(|n| n.has_tag_name("ResourceList"))
            .ok_or_else(|| SonyError::XmlParseError("未找到 ResourceList".to_string()))?;

        let mut audio_resources = Vec::new();
        let mut image_resources = Vec::new();
        let mut track_infos = Vec::new();

        // 解析音频录音
        for sound_recording in resource_list.children().filter(|n| n.has_tag_name("SoundRecording")) {
            match Self::parse_sound_recording_with_track_context(&sound_recording, base_dir, grid) {
                Ok((audio_resource, track_info)) => {
                    audio_resources.push(audio_resource);
                    track_infos.push(track_info);
                }
                Err(e) => {
                    debug!("解析音频录音失败（通常是缺少技术详情，这是正常的）: {}", e);
                }
            }
        }

        // 解析图片
        for image in resource_list.children().filter(|n| n.has_tag_name("Image")) {
            if let Ok(image_resource) = Self::parse_image_with_context(&image, base_dir, grid) {
                image_resources.push(image_resource);
            }
        }

        debug!("解析到 {} 个音频资源，{} 个图片资源，{} 个曲目",
               audio_resources.len(), image_resources.len(), track_infos.len());

        Ok((audio_resources, image_resources, track_infos))
    }
    
    /// 解析音频录音（向后兼容）
    fn parse_sound_recording(node: &Node) -> Result<AudioResource, SonyError> {
        Self::parse_sound_recording_with_context(node, "", "")
    }

    /// 解析音频录音（带上下文信息）
    fn parse_sound_recording_with_context(node: &Node, base_dir: &str, grid: &str) -> Result<AudioResource, SonyError> {
        let resource_reference = Self::get_text_content(node, "ResourceReference")?;
        
        // 获取音频详情 - 使用智能方法支持多种格式
        let details_node = Self::find_sound_recording_details_node(node)?;
        
        // 查找非预览的技术详情
        let tech_details = details_node
            .children()
            .filter(|n| n.has_tag_name("TechnicalSoundRecordingDetails"))
            .find(|n| {
                n.children()
                    .find(|child| child.has_tag_name("IsPreview"))
                    .and_then(|preview| preview.text())
                    .map(|text| text != "true")
                    .unwrap_or(true)
            })
            .ok_or_else(|| SonyError::XmlParseError("未找到非预览的技术详情".to_string()))?;
        
        // 解析文件信息
        let file_node = tech_details
            .children()
            .find(|n| n.has_tag_name("File"))
            .ok_or_else(|| SonyError::XmlParseError("未找到 File 节点".to_string()))?;
        
        let url = Self::get_text_content(&file_node, "URL")?;
        let md5 = Self::get_nested_text_content(&file_node, "HashSum", "HashSum")?;
        
        // 解析技术参数 - 需要从DeliveryFile子节点中获取
        let delivery_file = tech_details.children()
            .find(|n| n.has_tag_name("DeliveryFile"))
            .ok_or_else(|| SonyError::XmlParseError("DeliveryFile not found in TechnicalDetails".to_string()))?;

        let codec_type = Self::get_text_content(&delivery_file, "AudioCodecType")
            .unwrap_or_else(|_| "Unknown".to_string());

        // 解析BitRate并添加调试日志
        let bit_rate_str = Self::get_text_content(&delivery_file, "BitRate");
        let bit_rate = bit_rate_str
            .as_ref()
            .ok()
            .and_then(|s| {
                debug!("🎵 解析BitRate: '{}'", s);
                s.parse().ok()
            });

        let channels = Self::get_text_content(&delivery_file, "NumberOfChannels")
            .ok()
            .and_then(|s| s.parse().ok());

        // 解析SamplingRate并添加调试日志
        let sampling_rate_str = Self::get_text_content(&delivery_file, "SamplingRate");
        let sampling_rate = sampling_rate_str
            .as_ref()
            .ok()
            .and_then(|s| {
                debug!("🎵 解析SamplingRate: '{}'", s);
                s.parse().ok()
            });

        let bits_per_sample = Self::get_text_content(&delivery_file, "BitsPerSample")
            .ok()
            .and_then(|s| s.parse().ok());

        debug!("🎵 音频技术参数解析结果: bitrate={:?}, sampling_rate={:?}, channels={:?}",
               bit_rate, sampling_rate, channels);
        
        // 计算本地文件路径
        let local_file_path = if !base_dir.is_empty() && !grid.is_empty() {
            Self::map_url_to_local_path(&url, grid, base_dir).unwrap_or_else(|_| {
                warn!("无法映射 URL 到本地路径: {}", url);
                String::new()
            })
        } else {
            String::new()
        };

        Ok(AudioResource {
            resource_reference,
            url,
            local_file_path,
            md5,
            codec_type,
            bit_rate,
            channels,
            sampling_rate,
            bits_per_sample,
            is_preview: false,
            preview_details: None,
        })
    }
    
    /// 解析图片（向后兼容）
    fn parse_image(node: &Node) -> Result<ImageResource, SonyError> {
        Self::parse_image_with_context(node, "", "")
    }

    /// 解析图片（带上下文信息）
    fn parse_image_with_context(node: &Node, base_dir: &str, grid: &str) -> Result<ImageResource, SonyError> {
        let resource_reference = Self::get_text_content(node, "ResourceReference")?;
        let image_type = Self::get_text_content(node, "ImageType")?;
        
        // 获取图片详情
        let details_node = node
            .children()
            .find(|n| n.has_tag_name("ImageDetailsByTerritory"))
            .ok_or_else(|| SonyError::XmlParseError("未找到 ImageDetailsByTerritory".to_string()))?;
        
        let tech_details = details_node
            .children()
            .find(|n| n.has_tag_name("TechnicalImageDetails"))
            .ok_or_else(|| SonyError::XmlParseError("未找到 TechnicalImageDetails".to_string()))?;
        
        // 解析文件信息
        let file_node = tech_details
            .children()
            .find(|n| n.has_tag_name("File"))
            .ok_or_else(|| SonyError::XmlParseError("未找到 File 节点".to_string()))?;
        
        let url = Self::get_text_content(&file_node, "URL")?;
        let md5 = Self::get_nested_text_content(&file_node, "HashSum", "HashSum")?;
        
        // 解析图片参数
        let codec_type = Self::get_text_content(&tech_details, "ImageCodecType")
            .unwrap_or_else(|_| "Unknown".to_string());
        let height = Self::get_text_content(&tech_details, "ImageHeight")
            .ok()
            .and_then(|s| s.parse().ok());
        let width = Self::get_text_content(&tech_details, "ImageWidth")
            .ok()
            .and_then(|s| s.parse().ok());
        let resolution = Self::get_text_content(&tech_details, "ImageResolution")
            .ok()
            .and_then(|s| s.parse().ok());
        
        // 计算本地文件路径
        let local_file_path = if !base_dir.is_empty() && !grid.is_empty() {
            Self::map_url_to_local_path(&url, grid, base_dir).unwrap_or_else(|_| {
                warn!("无法映射 URL 到本地路径: {}", url);
                String::new()
            })
        } else {
            String::new()
        };

        Ok(ImageResource {
            resource_reference,
            image_type,
            url,
            local_file_path,
            md5,
            codec_type,
            height,
            width,
            resolution,
            is_preview: false,
        })
    }
    

    
    /// 获取文本内容的辅助方法
    fn get_text_content(parent: &Node, tag_name: &str) -> Result<String, SonyError> {
        parent
            .children()
            .find(|n| n.has_tag_name(tag_name))
            .and_then(|n| n.text())
            .map(|s| s.to_string())
            .ok_or_else(|| SonyError::XmlParseError(format!("未找到 {} 的文本内容", tag_name)))
    }
    
    /// 获取嵌套文本内容的辅助方法
    fn get_nested_text_content(parent: &Node, parent_tag: &str, child_tag: &str) -> Result<String, SonyError> {
        parent
            .children()
            .find(|n| n.has_tag_name(parent_tag))
            .and_then(|parent_node| {
                parent_node
                    .children()
                    .find(|n| n.has_tag_name(child_tag))
                    .and_then(|n| n.text())
            })
            .map(|s| s.to_string())
            .ok_or_else(|| SonyError::XmlParseError(
                format!("未找到 {}/{} 的文本内容", parent_tag, child_tag)
            ))
    }

    /// 获取多层嵌套路径的文本内容
    fn get_nested_path_text(parent: &Node, path: &[&str]) -> Result<String, SonyError> {
        let mut current_node = parent;
        let mut temp_nodes = Vec::new();

        for (i, &tag) in path.iter().enumerate() {
            if let Some(child) = current_node.children().find(|n| n.has_tag_name(tag)) {
                temp_nodes.push(child);
                current_node = &temp_nodes[i];
            } else {
                return Err(SonyError::XmlParseError(
                    format!("未找到路径 {} 中的标签 {}", path.join("/"), tag)
                ));
            }
        }

        current_node.text()
            .map(|s| s.to_string())
            .ok_or_else(|| SonyError::XmlParseError(
                format!("路径 {} 没有文本内容", path.join("/"))
            ))
    }







    /// 解析艺术家信息（支持多种 DDEX 格式）
    fn parse_artists(parent: &Node) -> Result<Vec<ArtistInfo>, SonyError> {
        let mut artists = Vec::new();

        for artist_node in parent.children().filter(|n| n.has_tag_name("DisplayArtist")) {
            // 尝试直接从 PartyName/FullName 获取名称（旧格式）
            let name = if let Ok(direct_name) = Self::get_nested_text_content(&artist_node, "PartyName", "FullName") {
                debug!("从 DisplayArtist/PartyName/FullName 获取艺术家名称");
                direct_name
            } else {
                // 新格式：通过 ArtistPartyReference 引用 PartyList 中的信息
                if let Some(party_ref) = Self::get_text_content(&artist_node, "ArtistPartyReference").ok() {
                    debug!("通过 ArtistPartyReference 查找艺术家: {}", party_ref);
                    // 需要在根节点的 PartyList 中查找对应的 Party
                    // 为了简化，我们先使用 DisplayArtistName（如果可用）
                    if let Some(display_name) = parent.children()
                        .find(|n| n.has_tag_name("DisplayArtistName"))
                        .and_then(|n| n.text())
                    {
                        debug!("使用 DisplayArtistName: {}", display_name);
                        display_name.to_string()
                    } else {
                        // 如果都没有，使用 party reference 作为名称
                        party_ref
                    }
                } else {
                    return Err(SonyError::XmlParseError("未找到艺术家名称或引用".to_string()));
                }
            };

            let sequence_number = artist_node
                .attribute("SequenceNumber")
                .and_then(|s| s.parse().ok());

            let mut roles = Vec::new();
            // 解析角色信息 - 支持多种角色标签
            for role_node in artist_node.children().filter(|n| {
                n.has_tag_name("DisplayArtistRole") ||
                n.has_tag_name("ArtistRole") ||
                n.has_tag_name("ArtisticRole")
            }) {
                if let Some(role_text) = role_node.text() {
                    roles.push(role_text.to_string());
                }
            }

            let language_code = artist_node
                .children()
                .find(|n| n.has_tag_name("PartyName"))
                .and_then(|n| n.attribute("LanguageAndScriptCode"))
                .map(|s| s.to_string());

            artists.push(ArtistInfo {
                name,
                roles,
                sequence_number,
                language_code,
            });
        }

        Ok(artists)
    }

    /// 解析音频录音，同时提取曲目信息（向后兼容）
    fn parse_sound_recording_with_track(node: &Node) -> Result<(AudioResource, TrackInfo), SonyError> {
        Self::parse_sound_recording_with_track_context(node, "", "")
    }

    /// 解析音频录音，同时提取曲目信息（带上下文信息）
    fn parse_sound_recording_with_track_context(node: &Node, base_dir: &str, grid: &str) -> Result<(AudioResource, TrackInfo), SonyError> {
        let resource_reference = Self::get_text_content(node, "ResourceReference")?;

        // 解析 ISRC - 支持多种格式
        let isrc = Self::get_nested_text_content(node, "SoundRecordingEdition", "ResourceId")
            .and_then(|_| Self::get_nested_path_text(node, &["SoundRecordingEdition", "ResourceId", "ISRC"]))
            .or_else(|_| Self::get_nested_text_content(node, "SoundRecordingId", "ISRC"))
            .unwrap_or_default();

        // 解析标题 - 使用智能方法支持多种格式
        let title = Self::get_title_text(node)?;

        // 解析时长
        let duration_str = Self::get_text_content(node, "Duration").unwrap_or_default();
        let duration = crate::importers::sony::utils::parse_duration(&duration_str).unwrap_or(0);

        // 解析语言
        let language = node
            .children()
            .find(|n| n.has_tag_name("LanguageOfPerformance"))
            .and_then(|n| n.text())
            .map(|s| s.to_string());

        // 获取音频详情 - 使用智能方法支持多种格式
        let details_node = Self::find_sound_recording_details_node(node)?;

        // 解析正式标题
        let formal_title = details_node
            .children()
            .filter(|n| n.has_tag_name("Title"))
            .find(|n| n.attribute("TitleType").map(|t| t == "FormalTitle").unwrap_or(false))
            .and_then(|n| n.children().find(|child| child.has_tag_name("TitleText")))
            .and_then(|n| n.text())
            .map(|s| s.to_string());

        // 解析艺术家
        let artists = Self::parse_artists(&details_node)?;

        // 解析贡献者
        let contributors = Self::parse_contributors(&details_node)?;

        // 解析流派
        let genre = Self::get_nested_text_content(&details_node, "Genre", "GenreText").ok();

        // 查找技术详情 - 支持多种格式
        let tech_details = node
            .children()
            .find(|n| n.has_tag_name("SoundRecordingEdition"))
            .and_then(|edition| edition.children().find(|n| n.has_tag_name("TechnicalDetails")))
            .or_else(|| {
                details_node
                    .children()
                    .filter(|n| n.has_tag_name("TechnicalSoundRecordingDetails"))
                    .find(|n| {
                        n.children()
                            .find(|child| child.has_tag_name("IsPreview"))
                            .and_then(|preview| preview.text())
                            .map(|text| text != "true")
                            .unwrap_or(true)
                    })
            })
            .ok_or_else(|| SonyError::XmlParseError("未找到技术详情".to_string()))?;

        // 解析文件信息 - 支持多种格式
        let file_node = tech_details
            .children()
            .find(|n| n.has_tag_name("DeliveryFile"))
            .and_then(|delivery| delivery.children().find(|n| n.has_tag_name("File")))
            .or_else(|| tech_details.children().find(|n| n.has_tag_name("File")))
            .ok_or_else(|| SonyError::XmlParseError("未找到 File 节点".to_string()))?;

        let url = Self::get_text_content(&file_node, "URI")
            .or_else(|_| Self::get_text_content(&file_node, "URL"))?;
        let md5 = Self::get_nested_text_content(&file_node, "HashSum", "HashSumValue")
            .or_else(|_| Self::get_nested_text_content(&file_node, "HashSum", "HashSum"))?;

        // 解析技术参数 - 需要从DeliveryFile子节点中获取
        let delivery_file = tech_details.children()
            .find(|n| n.has_tag_name("DeliveryFile"))
            .ok_or_else(|| SonyError::XmlParseError("DeliveryFile not found in TechnicalDetails".to_string()))?;

        let codec_type = Self::get_text_content(&delivery_file, "AudioCodecType")
            .unwrap_or_else(|_| "Unknown".to_string());

        // 解析BitRate并添加调试日志
        let bit_rate_str = Self::get_text_content(&delivery_file, "BitRate");
        let bit_rate = bit_rate_str
            .as_ref()
            .ok()
            .and_then(|s| {
                debug!("🎵 解析BitRate: '{}'", s);
                s.parse().ok()
            });

        let channels = Self::get_text_content(&delivery_file, "NumberOfChannels")
            .ok()
            .and_then(|s| s.parse().ok());

        // 解析SamplingRate并添加调试日志
        let sampling_rate_str = Self::get_text_content(&delivery_file, "SamplingRate");
        let sampling_rate = sampling_rate_str
            .as_ref()
            .ok()
            .and_then(|s| {
                debug!("🎵 解析SamplingRate: '{}'", s);
                s.parse().ok()
            });

        let bits_per_sample = Self::get_text_content(&delivery_file, "BitsPerSample")
            .ok()
            .and_then(|s| s.parse().ok());

        debug!("🎵 音频技术参数解析结果: bitrate={:?}, sampling_rate={:?}, channels={:?}",
               bit_rate, sampling_rate, channels);

        // 计算本地文件路径
        let local_file_path = if !base_dir.is_empty() && !grid.is_empty() {
            Self::map_url_to_local_path(&url, grid, base_dir).unwrap_or_else(|_| {
                warn!("无法映射 URL 到本地路径: {}", url);
                String::new()
            })
        } else {
            String::new()
        };

        let audio_resource = AudioResource {
            resource_reference: resource_reference.clone(),
            url,
            local_file_path,
            md5,
            codec_type,
            bit_rate,
            channels,
            sampling_rate,
            bits_per_sample,
            is_preview: false,
            preview_details: None,
        };

        let track_info = TrackInfo {
            resource_reference,
            isrc,
            title,
            formal_title,
            duration,
            sequence_number: 1, // 将在后续步骤中更新
            artists,
            contributors,
            language,
            genre,
        };

        Ok((audio_resource, track_info))
    }

    /// 解析贡献者信息
    fn parse_contributors(parent: &Node) -> Result<Vec<ContributorInfo>, SonyError> {
        let mut contributors = Vec::new();

        for contributor_node in parent.children().filter(|n| n.has_tag_name("ResourceContributor")) {
            let name = Self::get_nested_text_content(&contributor_node, "PartyName", "FullName")?;
            let sequence_number = contributor_node
                .attribute("SequenceNumber")
                .and_then(|s| s.parse().ok());

            for role_node in contributor_node.children().filter(|n| n.has_tag_name("ResourceContributorRole")) {
                let role = role_node.text().unwrap_or("Unknown").to_string();
                let namespace = role_node.attribute("Namespace").map(|s| s.to_string());
                let user_defined_value = role_node.attribute("UserDefinedValue").map(|s| s.to_string());

                contributors.push(ContributorInfo {
                    name: name.clone(),
                    role,
                    sequence_number,
                    namespace,
                    user_defined_value,
                });
            }
        }

        Ok(contributors)
    }

    /// 更新曲目序号信息
    fn update_track_sequence_numbers(root: &Node, track_infos: &mut [TrackInfo]) -> Result<(), SonyError> {
        debug!("更新曲目序号信息");

        let mut resource_to_sequence: HashMap<String, i32> = HashMap::new();

        // 从 ReleaseList 中获取曲目顺序
        if let Some(release_list) = root.children().find(|n| n.has_tag_name("ReleaseList")) {
            if let Some(main_release) = release_list.children()
                .filter(|n| n.has_tag_name("Release"))
                .find(|n| n.attribute("IsMainRelease").map(|attr| attr == "true").unwrap_or(false))
                .or_else(|| release_list.children().find(|n| n.has_tag_name("Release"))) {

                if let Some(details_node) = main_release.children().find(|n| n.has_tag_name("ReleaseDetailsByTerritory")) {
                    if let Some(resource_group) = details_node.children().find(|n| n.has_tag_name("ResourceGroup")) {
                        for content_item in resource_group.descendants().filter(|n| n.has_tag_name("ResourceGroupContentItem")) {
                            if let (Ok(seq_str), Ok(resource_ref)) = (
                                Self::get_text_content(&content_item, "SequenceNumber"),
                                Self::get_text_content(&content_item, "ReleaseResourceReference")
                            ) {
                                if let Ok(seq_num) = seq_str.parse::<i32>() {
                                    resource_to_sequence.insert(resource_ref, seq_num);
                                }
                            }
                        }
                    }
                }
            }
        }

        // 更新曲目序号
        debug!("找到的资源序号映射: {:?}", resource_to_sequence);
        for track_info in track_infos.iter_mut() {
            if let Some(&sequence_number) = resource_to_sequence.get(&track_info.resource_reference) {
                debug!("更新曲目 {} 的序号: {} -> {}", track_info.title, track_info.sequence_number, sequence_number);
                track_info.sequence_number = sequence_number;
            } else {
                debug!("未找到曲目 {} (资源引用: {}) 的序号", track_info.title, track_info.resource_reference);
            }
        }

        // 按序号排序
        track_infos.sort_by_key(|t| t.sequence_number);

        debug!("曲目序号更新完成");
        Ok(())
    }

    /// 解析专辑信息（简化版本，从 ReleaseList 中解析）
    fn parse_album_info(root: &Node) -> Result<AlbumInfo, SonyError> {
        debug!("解析专辑信息");

        let release_list = root
            .children()
            .find(|n| n.has_tag_name("ReleaseList"))
            .ok_or_else(|| SonyError::XmlParseError("未找到 ReleaseList".to_string()))?;

        // 查找主发行版本
        let main_release = release_list
            .children()
            .filter(|n| n.has_tag_name("Release"))
            .find(|n| {
                n.attribute("IsMainRelease")
                    .map(|attr| attr == "true")
                    .unwrap_or(false)
            })
            .or_else(|| release_list.children().find(|n| n.has_tag_name("Release")))
            .ok_or_else(|| SonyError::XmlParseError("未找到 Release".to_string()))?;

        // 解析发行 ID
        let release_id_node = main_release
            .children()
            .find(|n| n.has_tag_name("ReleaseId"))
            .ok_or_else(|| SonyError::XmlParseError("未找到 ReleaseId".to_string()))?;

        let grid = Self::get_text_content(&release_id_node, "GRid")?;
        let icpn = Self::get_text_content(&release_id_node, "ICPN").ok();
        let catalog_number = Self::get_text_content(&release_id_node, "CatalogNumber").ok();

        // 解析标题 - 使用智能方法支持多种格式
        let title = Self::get_title_text(&main_release)?;

        // 解析发行详情 - 支持多种格式
        let details_node = main_release
            .children()
            .find(|n| n.has_tag_name("ReleaseDetailsByTerritory"))
            .unwrap_or(main_release); // 如果没有找到，直接使用 main_release

        let display_artist_name = Self::get_text_content(&details_node, "DisplayArtistName")?;

        // 解析标签名称 - 支持多种格式
        let label_name = Self::get_text_content(&details_node, "LabelName")
            .or_else(|_| {
                // 尝试从 ReleaseLabelReference 获取
                if let Some(label_ref) = details_node
                    .children()
                    .find(|n| n.has_tag_name("ReleaseLabelReference"))
                    .and_then(|n| n.text())
                {
                    Ok(label_ref.to_string())
                } else {
                    Err(SonyError::XmlParseError("未找到标签名称".to_string()))
                }
            })?;
        let release_type = Self::get_text_content(&main_release, "ReleaseType")
            .unwrap_or_else(|_| "Album".to_string());

        // 解析正式标题
        let formal_title = details_node
            .children()
            .filter(|n| n.has_tag_name("Title"))
            .find(|n| n.attribute("TitleType").map(|t| t == "FormalTitle").unwrap_or(false))
            .and_then(|n| n.children().find(|child| child.has_tag_name("TitleText")))
            .and_then(|n| n.text())
            .map(|s| s.to_string());

        // 解析艺术家
        let artists = Self::parse_artists(&details_node)?;

        // 解析其他信息
        let release_date = Self::get_text_content(&main_release, "OriginalReleaseDate")
            .or_else(|_| Self::get_text_content(&main_release, "GlobalOriginalReleaseDate"))
            .ok();
        let duration_str = Self::get_text_content(&main_release, "Duration").unwrap_or_default();
        let duration = crate::importers::sony::utils::parse_duration(&duration_str).unwrap_or(0);

        let copyright = Self::get_nested_text_content(&main_release, "PLine", "PLineText")
            .unwrap_or_else(|_| "".to_string());

        let genre = Self::get_nested_text_content(&details_node, "Genre", "GenreText").ok();
        let parental_warning = Self::get_text_content(&details_node, "ParentalWarningType").ok();

        Ok(AlbumInfo {
            grid,
            icpn,
            catalog_number,
            title,
            formal_title,
            display_artist_name,
            artists,
            label_name,
            release_type,
            release_date,
            duration,
            copyright,
            genre,
            parental_warning,
        })
    }
}
