//! Sony DDEX 解析状态跟踪器 - 简化版
//! 
//! 只使用本地状态文件，无全局索引

use super::SonyError;
use super::simple_status_manager::{SimpleStatusManager, StatusStatistics};
use log::{debug, info, warn};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use walkdir::WalkDir;

/// 解析状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ParseStatus {
    /// 未开始解析
    NotStarted,
    /// 正在解析中
    InProgress,
    /// 解析成功
    Success,
    /// 解析失败
    Failed,
    /// 已跳过（如重复文件）
    Skipped,
}

/// 解析结果摘要
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParseSummary {
    /// 专辑标题
    pub album_title: String,
    /// GRid 标识符
    pub grid: String,
    /// 曲目数量
    pub track_count: usize,
    /// 音频资源数量
    pub audio_resource_count: usize,
    /// 图片资源数量
    pub image_resource_count: usize,
}

/// 简化的解析状态跟踪器
pub struct SimpleParseTracker {
    /// 简化状态管理器
    status_manager: SimpleStatusManager,
    /// 根目录路径
    root_dir: String,
}

impl SimpleParseTracker {
    /// 创建新的简化解析跟踪器
    pub async fn new(status_dir: &str, root_dir: &str) -> Result<Self, SonyError> {
        let status_manager = SimpleStatusManager::new("sony", status_dir);
        
        info!("🔍 初始化简化状态跟踪器...");
        
        // 扫描现有状态文件
        let status_map = status_manager.scan_all_status(root_dir).await
            .map_err(|e| SonyError::ConversionError(format!("Failed to scan status files: {}", e)))?;
        
        info!("✅ 找到 {} 个已处理文件", status_map.len());
        
        Ok(Self {
            status_manager,
            root_dir: root_dir.to_string(),
        })
    }
    
    /// 获取文件解析状态
    pub async fn get_status(&self, file_path: &str) -> Option<ParseStatus> {
        self.status_manager.get_status(file_path).await
    }
    
    /// 检查文件是否需要解析
    pub async fn should_parse_file(&self, file_path: &str) -> Result<bool, SonyError> {
        match self.status_manager.get_status(file_path).await {
            Some(ParseStatus::Success) => {
                debug!("文件已成功解析，跳过: {}", file_path);
                Ok(false)
            }
            Some(ParseStatus::InProgress) => {
                warn!("文件正在解析中，可能是上次异常退出: {}", file_path);
                Ok(true) // 重新解析
            }
            Some(ParseStatus::Failed) => {
                info!("文件上次解析失败，重新尝试: {}", file_path);
                Ok(true)
            }
            _ => {
                info!("新文件，需要解析: {}", file_path);
                Ok(true)
            }
        }
    }
    
    /// 标记文件开始解析
    pub async fn mark_parse_start(&self, file_path: &str) -> Result<(), SonyError> {
        self.status_manager.update_status(file_path, ParseStatus::InProgress).await
            .map_err(|e| SonyError::ConversionError(format!("Failed to update status: {}", e)))?;

        info!("标记文件开始解析: {}", file_path);
        Ok(())
    }
    
    /// 标记文件解析成功
    pub async fn mark_parse_success(&self, file_path: &str, _summary: ParseSummary) -> Result<(), SonyError> {
        self.status_manager.update_status(file_path, ParseStatus::Success).await
            .map_err(|e| SonyError::ConversionError(format!("Failed to update status: {}", e)))?;

        info!("标记文件解析成功: {}", file_path);
        Ok(())
    }

    /// 标记文件解析失败
    pub async fn mark_parse_failed(&self, file_path: &str, error: &str) -> Result<(), SonyError> {
        self.status_manager.update_status(file_path, ParseStatus::Failed).await
            .map_err(|e| SonyError::ConversionError(format!("Failed to update status: {}", e)))?;

        warn!("标记文件解析失败: {} - {}", file_path, error);
        Ok(())
    }

    /// 获取解析统计信息
    pub async fn get_statistics(&self) -> HashMap<ParseStatus, usize> {
        match self.status_manager.get_statistics(&self.root_dir).await {
            Ok(stats) => {
                let mut result = HashMap::new();
                result.insert(ParseStatus::Success, stats.success_count);
                result.insert(ParseStatus::Failed, stats.failed_count);
                result.insert(ParseStatus::InProgress, stats.in_progress_count);
                result
            }
            Err(e) => {
                warn!("获取统计信息失败: {}", e);
                HashMap::new()
            }
        }
    }

    /// 列出所有需要解析的文件
    pub fn list_files_to_parse(&self, directory: &str) -> Result<Vec<String>, SonyError> {
        let mut files_to_parse = Vec::new();

        // 扫描目录中的所有 XML 文件
        for entry in WalkDir::new(directory) {
            let entry = entry.map_err(|e| SonyError::IoError(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!("目录扫描失败: {}", e)
            )))?;

            if entry.file_type().is_file() {
                if let Some(extension) = entry.path().extension() {
                    if extension == "xml" {
                        let file_path = entry.path().to_string_lossy().to_string();
                        files_to_parse.push(file_path);
                    }
                }
            }
        }

        Ok(files_to_parse)
    }
    
    /// 获取详细统计信息
    pub async fn get_detailed_statistics(&self) -> Result<StatusStatistics, SonyError> {
        self.status_manager.get_statistics(&self.root_dir).await
            .map_err(|e| SonyError::ConversionError(format!("Failed to get statistics: {}", e)))
    }
}
