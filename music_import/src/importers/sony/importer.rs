use anyhow::Result;
use std::collections::HashMap;
use std::fs;
use crate::config::Config;
use crate::importers::{BaseImporter, Importer, ImportResult};
use crate::utils::db::Database;
use log::{info, error, debug, warn};

// 导入我们的新模块
use super::ddex_parser::DdexParser;
use super::simple_parse_tracker::{SimpleParseTracker, ParseSummary};
use super::daily_logger::DailyLogger;
use super::SonyError;

pub struct SonyImporter {
    base: BaseImporter,
    parse_tracker: Option<SimpleParseTracker>,
    daily_logger: Option<DailyLogger>,
    database: Option<Database>,
}

impl SonyImporter {
    pub fn new(config: Config, date: String) -> Self {
        Self {
            base: BaseImporter::new(config, date),
            parse_tracker: None,
            daily_logger: None,
            database: None,
        }
    }

    /// 生成基于专辑ID的稳定时间戳
    ///
    /// 使用专辑ID的哈希值生成一个看起来像时间戳的稳定字符串
    /// 这样相同的专辑ID总是生成相同的"时间戳"
    fn generate_stable_timestamp(&self, album_id: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        album_id.hash(&mut hasher);
        let hash = hasher.finish();

        // 将哈希值转换为看起来像时间戳的格式
        // 格式：YYYYMMDDHHMMSSXXX
        let year = 2022 + (hash % 3) as u32; // 2022-2024
        let month = 1 + (hash >> 8) % 12; // 1-12
        let day = 1 + (hash >> 16) % 28; // 1-28 (避免月份天数问题)
        let hour = (hash >> 24) % 24; // 0-23
        let minute = (hash >> 32) % 60; // 0-59
        let second = (hash >> 40) % 60; // 0-59
        let millis = (hash >> 48) % 1000; // 0-999

        format!("{:04}{:02}{:02}{:02}{:02}{:02}{:03}",
                year, month, day, hour, minute, second, millis)
    }

    pub async fn initialize(&mut self) -> Result<()> {
        // 初始化解析状态跟踪器（必须成功）
        let status_dir = format!("{}/status", self.base.config.log_folder);
        self.parse_tracker = Some(SimpleParseTracker::new(&status_dir, &self.base.config.root_folder).await
            .map_err(|e| anyhow::anyhow!("Parse tracker initialization failed: {}", e))?);

        // 初始化每日日志记录器（必须成功）
        let daily_log_dir = format!("{}/daily", self.base.config.log_folder);
        self.daily_logger = Some(DailyLogger::new(&daily_log_dir)
            .map_err(|e| anyhow::anyhow!("Daily logger initialization failed: {}", e))?);

        // 初始化 gRPC 客户端（必需）
        info!("🔗 检查 gRPC 服务器连通性...");
        self.base.initialize_grpc_client().await
            .map_err(|e| anyhow::anyhow!("gRPC client initialization failed: {}", e))?;
        info!("✅ gRPC 服务器连接成功");

        // 初始化数据库连接（必需）
        info!("🗄️ 初始化 MySQL 数据库连接...");
        let database = Database::new(&self.base.config.database)
            .map_err(|e| anyhow::anyhow!("Database initialization failed: {}", e))?;

        self.database = Some(database);
        info!("✅ MySQL 数据库连接成功");

        info!("🎵 Sony 导入器初始化完成");
        Ok(())
    }



    /// 扫描并解析所有 DDEX XML 文件
    pub async fn scan_and_parse_all(&mut self) -> Result<()> {
        info!("🔍 开始扫描 Sony DDEX XML 文件...");

        // 先获取文件列表
        let xml_files = {
            let tracker = self.parse_tracker.as_mut()
                .ok_or_else(|| anyhow::anyhow!("Parse tracker not initialized"))?;
            tracker.list_files_to_parse(&self.base.config.root_folder)
                .map_err(|e| anyhow::anyhow!("Failed to scan XML files: {}", e))?
        };

        info!("📁 找到 {} 个 XML 文件", xml_files.len());

        let mut processed = 0;
        let mut skipped = 0;
        let mut failed = 0;

        for xml_file in &xml_files {
            info!("📄 检查文件: {}", xml_file);

            // 检查是否需要解析
            let should_parse = {
                let tracker = self.parse_tracker.as_ref()
                    .ok_or_else(|| anyhow::anyhow!("Parse tracker not initialized"))?;
                tracker.should_parse_file(&xml_file).await
                    .map_err(|e| anyhow::anyhow!("Failed to check parse status: {}", e))?
            };

            if !should_parse {
                info!("⏭️  跳过已解析的文件: {}", xml_file);

                // 记录跳过的文件
                {
                    let daily_logger = self.daily_logger.as_mut()
                        .ok_or_else(|| anyhow::anyhow!("Daily logger not initialized"))?;
                    daily_logger.record_skipped(&xml_file, "文件已成功解析")
                        .map_err(|e| anyhow::anyhow!("Failed to record skipped file: {}", e))?;
                }

                skipped += 1;
                continue;
            }

            // 开始解析
            info!("🎵 开始解析: {}", xml_file);
            match self.parse_single_file(&xml_file).await {
                Ok(summary) => {
                    let tracker = self.parse_tracker.as_mut()
                        .ok_or_else(|| anyhow::anyhow!("Parse tracker not initialized"))?;
                    tracker.mark_parse_success(&xml_file, summary).await
                        .map_err(|e| anyhow::anyhow!("Failed to mark success: {}", e))?;
                    processed += 1;
                    info!("✅ 解析成功: {}", xml_file);
                }
                Err(e) => {
                    let error_msg = format!("{}", e);

                    // 标记解析失败
                    {
                        let tracker = self.parse_tracker.as_mut()
                            .ok_or_else(|| anyhow::anyhow!("Parse tracker not initialized"))?;
                        tracker.mark_parse_failed(&xml_file, &error_msg).await
                            .map_err(|e| anyhow::anyhow!("Failed to mark failure: {}", e))?;
                    }

                    // 记录解析失败并完成处理
                    {
                        let daily_logger = self.daily_logger.as_mut()
                            .ok_or_else(|| anyhow::anyhow!("Daily logger not initialized"))?;
                        daily_logger.record_parse_failed(&xml_file, &error_msg)
                            .map_err(|e| anyhow::anyhow!("Failed to record parse failure: {}", e))?;
                        daily_logger.finish_processing(&xml_file)
                            .map_err(|e| anyhow::anyhow!("Failed to finish daily logging: {}", e))?;
                    }

                    failed += 1;
                    error!("❌ 解析失败: {} - {}", xml_file, error_msg);
                }
            }
        }

        // 简化版状态管理器无需强制写入（每次更新都直接写入本地文件）
        info!("✅ 所有状态已自动保存到本地文件");

        // 显示统计信息
        info!("📊 解析完成统计:");
        info!("   ✅ 成功解析: {} 个文件", processed);
        info!("   ⏭️  跳过文件: {} 个文件", skipped);
        info!("   ❌ 失败文件: {} 个文件", failed);

        // 显示详细统计
        let stats = {
            let tracker = self.parse_tracker.as_ref()
                .ok_or_else(|| anyhow::anyhow!("Parse tracker not initialized"))?;
            tracker.get_statistics().await
        };
        for (status, count) in stats {
            info!("   {:?}: {} 个文件", status, count);
        }

        Ok(())
    }

    /// 解析单个 XML 文件并完成完整处理链路
    async fn parse_single_file(&mut self, xml_file: &str) -> Result<ParseSummary> {
        // 开始记录处理
        {
            let daily_logger = self.daily_logger.as_mut()
                .ok_or_else(|| anyhow::anyhow!("Daily logger not initialized"))?;
            daily_logger.start_processing(xml_file)
                .map_err(|e| anyhow::anyhow!("Failed to start daily logging: {}", e))?;
        }

        // 标记开始解析
        {
            let tracker = self.parse_tracker.as_mut()
                .ok_or_else(|| anyhow::anyhow!("Parse tracker not initialized"))?;
            tracker.mark_parse_start(xml_file).await
                .map_err(|e| anyhow::anyhow!("Failed to mark parse start: {}", e))?;
        }

        // 第一步：解析 DDEX XML
        info!("🎵 步骤1: 解析 DDEX XML 文件: {}", xml_file);
        let parsed_release = self.parse_ddex_xml(xml_file)?;

        // 创建解析摘要
        let summary = ParseSummary {
            album_title: parsed_release.album_info.title.clone(),
            grid: parsed_release.album_info.grid.clone(),
            track_count: parsed_release.tracks.len(),
            audio_resource_count: parsed_release.audio_resources.len(),
            image_resource_count: parsed_release.image_resources.len(),
        };

        info!("📀 解析成功: {} (GRid: {}, 曲目: {}, 音频: {}, 图片: {})",
              summary.album_title,
              summary.grid,
              summary.track_count,
              summary.audio_resource_count,
              summary.image_resource_count);

        // 记录解析成功
        {
            let daily_logger = self.daily_logger.as_mut()
                .ok_or_else(|| anyhow::anyhow!("Daily logger not initialized"))?;
            daily_logger.record_parse_success(
                xml_file,
                &summary.album_title,
                &summary.grid,
                summary.track_count,
                summary.audio_resource_count,
                summary.image_resource_count,
            ).map_err(|e| anyhow::anyhow!("Failed to record parse success: {}", e))?;
        }

        // 第二步：上传加密音频和图片资源到 NAS
        info!("🚀 步骤2: 上传加密音频和图片资源到 NAS");
        let nas_file_paths = self.upload_resources_to_nas(&parsed_release).await?;

        // 记录上传成功
        {
            let daily_logger = self.daily_logger.as_mut()
                .ok_or_else(|| anyhow::anyhow!("Daily logger not initialized"))?;
            daily_logger.record_upload_success(xml_file, &nas_file_paths)
                .map_err(|e| anyhow::anyhow!("Failed to record upload success: {}", e))?;
        }

        // 第三步：DDEX部分主要属性与 NAS 文件路径上传至 DB
        info!("💾 步骤3: 上传 DDEX 属性与 NAS 文件路径至数据库");
        self.upload_metadata_to_db(&parsed_release, &nas_file_paths).await?;

        // 记录数据库保存成功并完成处理
        {
            let daily_logger = self.daily_logger.as_mut()
                .ok_or_else(|| anyhow::anyhow!("Daily logger not initialized"))?;
            daily_logger.record_database_success(xml_file)
                .map_err(|e| anyhow::anyhow!("Failed to record database success: {}", e))?;
            daily_logger.finish_processing(xml_file)
                .map_err(|e| anyhow::anyhow!("Failed to finish daily logging: {}", e))?;
        }

        info!("✅ 文件处理完成: {}", xml_file);
        Ok(summary)
    }

    /// 上传资源文件到 NAS（通过 gRPC）
    async fn upload_resources_to_nas(&mut self, parsed_release: &super::ParsedRelease) -> Result<Vec<String>> {
        let mut nas_file_paths = Vec::new();
        info!("🌐 开始上传文件到 NAS 服务器");

        // 处理音频文件上传
        for audio_resource in &parsed_release.audio_resources {
            info!("🎧 准备上传音频文件: {} (本地: {})",
                  audio_resource.resource_reference,
                  audio_resource.local_file_path);

            // 使用 PathGenerator 生成正确的路径格式
            // 格式：SONY/YYYYMMDD/AlbumID_Timestamp/FileID.extension
            let file_id = &audio_resource.resource_reference;
            let date = chrono::Utc::now().format("%Y%m%d").to_string();

            // 根据加密配置确定扩展名
            let extension = match self.base.config.encryption.version.as_str() {
                "legacy" => "bin",
                "v1" => "encrypted",
                _ => "encrypted", // 默认使用新版
            };

            // 生成专辑目录（使用稳定的时间戳）
            let album_id = &parsed_release.album_info.grid;
            let timestamp = self.generate_stable_timestamp(&parsed_release.album_info.grid);
            let album_dir = format!("{}_{}", album_id, timestamp);

            let nas_path = format!("audio/SONY/{}/{}/{}.{}",
                                   date,
                                   album_dir,
                                   file_id,
                                   extension);

            // 检查本地文件是否存在
            if !audio_resource.local_file_path.is_empty() &&
               std::path::Path::new(&audio_resource.local_file_path).exists() {
                // 上传本地文件到 NAS
                self.base.upload_file(&audio_resource.local_file_path, &nas_path).await?;
                info!("✅ 音频文件上传成功: {} -> {}", audio_resource.local_file_path, nas_path);
            } else {
                return Err(anyhow::anyhow!("本地音频文件不存在: {}", audio_resource.local_file_path));
            }

            nas_file_paths.push(nas_path);
        }

        // 处理图片文件上传
        for image_resource in &parsed_release.image_resources {
            info!("🖼️  准备上传图片文件: {} (本地: {})",
                  image_resource.resource_reference,
                  image_resource.local_file_path);

            // 图片文件通常不加密，保持原始扩展名
            let file_id = &image_resource.resource_reference;
            let date = chrono::Utc::now().format("%Y%m%d").to_string();

            // 生成专辑目录（与音频文件使用相同的稳定时间戳）
            let album_id = &parsed_release.album_info.grid;
            let timestamp = self.generate_stable_timestamp(&parsed_release.album_info.grid);
            let album_dir = format!("{}_{}", album_id, timestamp);

            // 图片保持原始扩展名（通常是 .jpg, .png 等）
            let nas_path = format!("image/SONY/{}/{}/{}",
                                   date,
                                   album_dir,
                                   file_id);

            // 检查本地文件是否存在
            if !image_resource.local_file_path.is_empty() &&
               std::path::Path::new(&image_resource.local_file_path).exists() {
                // 上传本地文件到 NAS
                self.base.upload_file(&image_resource.local_file_path, &nas_path).await?;
                info!("✅ 图片文件上传成功: {} -> {}", image_resource.local_file_path, nas_path);
            } else {
                return Err(anyhow::anyhow!("本地图片文件不存在: {}", image_resource.local_file_path));
            }

            nas_file_paths.push(nas_path);
        }

        info!("🎯 资源上传完成，共 {} 个文件", nas_file_paths.len());
        Ok(nas_file_paths)
    }

    /// 上传元数据到数据库
    async fn upload_metadata_to_db(&self, parsed_release: &super::ParsedRelease, nas_file_paths: &[String]) -> Result<()> {
        info!("📊 准备上传元数据到数据库");

        let db = self.database.as_ref()
            .ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        // 第一步：插入专辑信息
        let album_id = self.insert_album(db, &parsed_release.album_info).await?;
        info!("✅ 专辑插入成功，ID: {}", album_id);

        // 第二步：处理艺术家信息
        let mut artist_id_map = HashMap::new();
        for artist in &parsed_release.album_info.artists {
            let artist_id = self.insert_or_get_artist(db, artist).await?;
            artist_id_map.insert(artist.name.clone(), artist_id);
        }

        // 第三步：插入曲目信息
        for (i, track) in parsed_release.tracks.iter().enumerate() {
            let nas_file_path = nas_file_paths.get(i).map(|s| s.as_str()).unwrap_or("");
            let track_id = self.insert_track(db, track, album_id, nas_file_path).await?;

            // 处理曲目艺术家关联
            for artist in &track.artists {
                let artist_id = if let Some(&id) = artist_id_map.get(&artist.name) {
                    id
                } else {
                    let id = self.insert_or_get_artist(db, artist).await?;
                    artist_id_map.insert(artist.name.clone(), id);
                    id
                };

                self.insert_track_artist(db, track_id, artist_id, artist).await?;
            }
        }

        info!("💾 元数据上传完成 - 专辑ID: {}, 曲目数: {}", album_id, parsed_release.tracks.len());
        Ok(())
    }

    /// 插入专辑信息到数据库（如果已存在则返回现有ID）
    async fn insert_album(&self, db: &Database, album_info: &super::AlbumInfo) -> Result<i64> {
        // 首先检查专辑是否已存在
        let query_select = "SELECT id FROM albums WHERE grid = ?";

        debug!("查询现有专辑: GRid = {}", album_info.grid);
        match db.query(query_select, (&album_info.grid,)).await {
            Ok(rows) => {
                debug!("查询返回 {} 行结果", rows.len());
                if let Some(row) = rows.first() {
                    if let Some(id) = row.get::<i64, _>("id") {
                        info!("专辑已存在，使用现有ID: {} (GRid: {}, Title: {})",
                              id, album_info.grid, album_info.title);
                        return Ok(id);
                    }
                }
                debug!("专辑不存在，准备插入新专辑");
            }
            Err(e) => {
                warn!("查询现有专辑失败: {}", e);
                // 继续尝试插入
            }
        }

        // 专辑不存在，执行插入
        let query_insert = r#"
            INSERT INTO albums (
                title, grid, icpn, release_type, release_date,
                duration, copyright, label_name, display_artist_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        "#;

        let params = (
            &album_info.title,
            &album_info.grid,
            album_info.icpn.as_ref(),
            &album_info.release_type,
            album_info.release_date.as_ref(),
            album_info.duration,
            &album_info.copyright,
            &album_info.label_name,
            &album_info.display_artist_name,
        );

        let album_id = db.execute_insert(query_insert, params).await
            .map_err(|e| anyhow::anyhow!("Failed to insert album '{}' (GRid: {}): {}",
                                        album_info.title, album_info.grid, e))?;

        info!("成功插入新专辑: {} (ID: {}, GRid: {})",
              album_info.title, album_id, album_info.grid);
        Ok(album_id)
    }

    /// 插入或获取艺术家信息
    async fn insert_or_get_artist(&self, db: &Database, artist: &super::ArtistInfo) -> Result<i64> {
        // 首先尝试根据名称查找现有艺术家
        let query_select = "SELECT id FROM artists WHERE canonical_name = ?";

        if let Ok(rows) = db.query(query_select, (&artist.name,)).await {
            if let Some(row) = rows.first() {
                if let Some(id) = row.get::<i64, _>("id") {
                    debug!("找到现有艺术家: {} (ID: {})", artist.name, id);
                    return Ok(id);
                }
            }
        }

        // 如果不存在，插入新艺术家
        let query_insert = r#"
            INSERT INTO artists (canonical_name)
            VALUES (?)
        "#;

        let artist_id = db.execute_insert(query_insert, (&artist.name,)).await
            .map_err(|e| anyhow::anyhow!("Failed to insert artist: {}", e))?;

        debug!("插入新艺术家: {} (ID: {})", artist.name, artist_id);

        // 插入艺术家名称到 artist_names 表
        self.insert_artist_name(db, artist_id, artist).await?;

        // TODO: 如果需要处理多语言名称，可以在这里添加逻辑
        // 从 PartyList 中查找该艺术家的所有语言版本名称

        Ok(artist_id)
    }

    /// 插入艺术家名称到 artist_names 表
    async fn insert_artist_name(&self, db: &Database, artist_id: i64, artist: &super::ArtistInfo) -> Result<()> {
        let query = r#"
            INSERT INTO artist_names (artist_id, name, language_code, is_primary)
            VALUES (?, ?, ?, ?)
        "#;

        let params = (
            artist_id,
            &artist.name,
            artist.language_code.as_ref(),
            true, // 设置为主要名称
        );

        db.execute(query, params).await
            .map_err(|e| anyhow::anyhow!("Failed to insert artist name: {}", e))?;

        debug!("插入艺术家名称: {} (artist_id: {}, language: {:?})",
               artist.name, artist_id, artist.language_code);
        Ok(())
    }

    /// 插入曲目信息到数据库（如果已存在则返回现有ID）
    async fn insert_track(&self, db: &Database, track: &super::TrackInfo, album_id: i64, nas_file_path: &str) -> Result<i64> {
        // 先检查 ISRC 是否已存在
        if !track.isrc.is_empty() {
            let query_select = "SELECT id FROM tracks WHERE isrc = ?";

            match db.query(query_select, (&track.isrc,)).await {
                Ok(rows) => {
                    if let Some(row) = rows.first() {
                        if let Some(id) = row.get::<i64, _>("id") {
                            info!("曲目已存在，使用现有ID: {} (ISRC: {}, Title: {})",
                                  id, track.isrc, track.title);
                            return Ok(id);
                        }
                    }
                }
                Err(e) => {
                    warn!("查询现有曲目失败: {}", e);
                    // 继续尝试插入
                }
            }
        }

        // 曲目不存在，执行插入
        let query_insert = r#"
            INSERT INTO tracks (
                album_id, title, isrc, duration, track_number, file_path
            ) VALUES (?, ?, ?, ?, ?, ?)
        "#;

        let params = (
            album_id,
            &track.title,
            &track.isrc,
            track.duration,
            track.sequence_number,
            if nas_file_path.is_empty() { None } else { Some(nas_file_path) },
        );

        let track_id = db.execute_insert(query_insert, params).await
            .map_err(|e| anyhow::anyhow!("Failed to insert track '{}' (ISRC: {}): {}",
                                        track.title, track.isrc, e))?;

        info!("成功插入新曲目: {} (ID: {}, ISRC: {})",
              track.title, track_id, track.isrc);
        Ok(track_id)
    }

    /// 插入曲目艺术家关联
    async fn insert_track_artist(&self, db: &Database, track_id: i64, artist_id: i64, artist: &super::ArtistInfo) -> Result<()> {
        let query = r#"
            INSERT INTO track_artists (track_id, artist_id, role, sequence_number)
            VALUES (?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE sequence_number = VALUES(sequence_number)
        "#;

        // 如果艺术家有角色信息，为每个角色创建记录
        if !artist.roles.is_empty() {
            for role in &artist.roles {
                let params = (
                    track_id,
                    artist_id,
                    Some(role.as_str()),
                    artist.sequence_number,
                );

                db.execute(query, params).await
                    .map_err(|e| anyhow::anyhow!("Failed to insert track artist: {}", e))?;

                debug!("关联曲目艺术家: track_id={}, artist_id={}, role={}", track_id, artist_id, role);
            }
        } else {
            // 如果没有角色信息，使用默认角色 "2"
            let params = (
                track_id,
                artist_id,
                Some("2"),
                artist.sequence_number,
            );

            db.execute(query, params).await
                .map_err(|e| anyhow::anyhow!("Failed to insert track artist: {}", e))?;

            debug!("关联曲目艺术家: track_id={}, artist_id={}, role=2 (默认)", track_id, artist_id);
        }

        Ok(())
    }




    /// 解析 DDEX XML 文件
    pub fn parse_ddex_xml(&self, xml_path: &str) -> Result<super::ParsedRelease, SonyError> {
        debug!("读取 XML 文件: {}", xml_path);
        let xml_content = fs::read_to_string(xml_path)
            .map_err(|e| SonyError::IoError(e))?;

        debug!("解析 DDEX XML 内容");
        DdexParser::parse_xml_with_context(&xml_content, xml_path)
    }








}

impl Importer for SonyImporter {
    fn start_import(&mut self) -> ImportResult {
        self.base.log("------------ Sony Import Process Start ------------", true);

        // 初始化导入器（包括 gRPC 客户端和解析跟踪器）
        if let Err(e) = tokio::task::block_in_place(|| {
            tokio::runtime::Handle::current().block_on(async {
                self.initialize().await
            })
        }) {
            self.base.log(&format!("Error: Failed to initialize importer: {}", e), true);
            return ImportResult::Failure(e.to_string());
        }

        // 使用新的文件级别处理流程
        match tokio::task::block_in_place(|| {
            tokio::runtime::Handle::current().block_on(async {
                self.scan_and_parse_all().await
            })
        }) {
            Ok(_) => {
                self.base.log("All files processed successfully", true);
                ImportResult::Success
            }
            Err(e) => {
                self.base.log(&format!("Failed to process files: {}", e), true);
                ImportResult::Failure(e.to_string())
            }
        }
    }

    fn send_email(&self) -> Result<()> {
        let subject = format!("Sony Music Import Report for {}", self.base.date);
        let body = format!("{:#?}", self.get_records());
        self.base.send_email_report(&subject, &body)
    }

    fn get_records(&self) -> HashMap<String, Vec<String>> {
        self.base.record.lock().unwrap().clone()
    }
}
