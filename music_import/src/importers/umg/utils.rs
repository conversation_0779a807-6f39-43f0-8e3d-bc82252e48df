//! UMG 工具函数和数据转换

use anyhow::Result;
use log::debug;

use crate::importers::models::{
    ParsedRelease as StandardParsedRelease,
    MessageHeader as StandardMessageHeader,
    AlbumInfo as StandardAlbumInfo,
    TrackInfo as StandardTrackInfo,
    AudioResource as StandardAudioResource,
    ImageResource as StandardImageResource,
    ProviderInfo,
    AudioFormat,
    ImageFormat,
    ImageType,
};

use super::{ParsedRelease, MessageHeader, AlbumInfo, TrackInfo, AudioResource, ImageResource, PartyInfo};

/// 将 UMG 特定的解析结果转换为标准格式
pub fn convert_umg_to_standard(umg_parsed: ParsedRelease, xml_path: &str) -> Result<StandardParsedRelease> {
    debug!("转换 UMG 解析结果为标准格式");

    let standard_message_header = convert_message_header(&umg_parsed.message_header);
    let standard_album_info = convert_album_info(&umg_parsed.album_info);
    let standard_tracks = convert_tracks(&umg_parsed.tracks, &umg_parsed.audio_resources);
    let standard_audio_resources = convert_audio_resources(&umg_parsed.audio_resources);
    let standard_image_resources = convert_image_resources(&umg_parsed.image_resources);

    // 创建提供商信息
    let provider_info = ProviderInfo {
        provider: "UMG".to_string(),
        source_file: xml_path.to_string(),
        parsed_at: chrono::Utc::now(),
        metadata: create_umg_metadata(&umg_parsed.party_list),
    };

    Ok(StandardParsedRelease {
        message_header: standard_message_header,
        album_info: standard_album_info,
        tracks: standard_tracks,
        audio_resources: standard_audio_resources,
        image_resources: standard_image_resources,
        provider_info,
    })
}

/// 转换消息头信息
fn convert_message_header(umg_header: &MessageHeader) -> StandardMessageHeader {
    StandardMessageHeader {
        message_thread_id: umg_header.message_thread_id.clone(),
        message_id: umg_header.message_id.clone(),
        sender: crate::importers::models::PartyInfo {
            party_id: umg_header.sender.party_id.clone(),
            party_name: umg_header.sender.party_name.clone(),
            party_type: umg_header.sender.party_type.clone(),
        },
        recipient: crate::importers::models::PartyInfo {
            party_id: umg_header.recipient.party_id.clone(),
            party_name: umg_header.recipient.party_name.clone(),
            party_type: umg_header.recipient.party_type.clone(),
        },
        created_datetime: umg_header.created_datetime.clone(),
        control_type: umg_header.control_type.clone(),
    }
}

/// 转换专辑信息
fn convert_album_info(umg_album: &AlbumInfo) -> StandardAlbumInfo {
    StandardAlbumInfo {
        title: umg_album.title.clone(),
        artist_name: umg_album.display_artist.clone(),
        release_date: umg_album.release_date.clone(),
        album_type: umg_album.release_type.clone(),
        genre: "Unknown".to_string(), // UMG 专辑级别可能没有流派信息
        label: umg_album.label_name.clone(),
        copyright: umg_album.copyright.clone(),
        // UMG 使用 ICPN 作为 UPC，数据库要求不能为空
        upc: if umg_album.icpn.is_empty() {
            Some("UMG_UNKNOWN".to_string())
        } else {
            Some(umg_album.icpn.clone())
        },
        grid: Some(umg_album.grid.clone()),
        description: umg_album.formal_title.clone(),
    }
}

/// 转换曲目信息
fn convert_tracks(umg_tracks: &[TrackInfo], umg_audio_resources: &[AudioResource]) -> Vec<StandardTrackInfo> {
    umg_tracks.iter().map(|umg_track| {
        // 提取主要艺术家名称
        let artist_name = if !umg_track.artists.is_empty() {
            umg_track.artists[0].name.clone()
        } else {
            "Unknown Artist".to_string()
        };

        // 提取作曲者和作词者
        let mut composer = None;
        let mut lyricist = None;
        let mut producer = None;

        for contributor in &umg_track.contributors {
            match contributor.role.to_lowercase().as_str() {
                "composer" | "songwriter" => {
                    if composer.is_none() {
                        composer = Some(contributor.name.clone());
                    }
                }
                "lyricist" | "writer" => {
                    if lyricist.is_none() {
                        lyricist = Some(contributor.name.clone());
                    }
                }
                "producer" => {
                    if producer.is_none() {
                        producer = Some(contributor.name.clone());
                    }
                }
                _ => {}
            }
        }

        // 创建提供商特定的元数据
        let mut provider_metadata = std::collections::HashMap::new();
        provider_metadata.insert("resource_reference".to_string(), umg_track.resource_reference.clone());
        
        if let Some(ref display_title) = umg_track.display_title {
            provider_metadata.insert("display_title".to_string(), display_title.clone());
        }
        
        if let Some(ref p_line) = umg_track.p_line {
            provider_metadata.insert("p_line".to_string(), p_line.clone());
        }
        
        if let Some(ref start_date) = umg_track.start_date {
            provider_metadata.insert("start_date".to_string(), start_date.clone());
        }
        
        if let Some(ref end_date) = umg_track.end_date {
            provider_metadata.insert("end_date".to_string(), end_date.clone());
        }

        provider_metadata.insert("is_explicit".to_string(), umg_track.is_explicit.to_string());

        // 从对应的音频资源中提取 bitrate 和 sample_rate
        let (bitrate, sample_rate) = umg_audio_resources
            .iter()
            .find(|audio| audio.resource_id == umg_track.resource_reference)
            .map(|audio| {
                let sample_rate_u32 = audio.sample_rate.as_ref()
                    .and_then(|sr| {
                        // UMG采样率可能是浮点数（如"44.1"），需要特殊处理
                        if let Ok(f_val) = sr.parse::<f32>() {
                            // 乘以10保持一位小数精度：44.1 -> 441
                            Some((f_val * 10.0) as u32)
                        } else {
                            sr.parse::<u32>().ok()
                        }
                    });
                (audio.bit_rate, sample_rate_u32)
            })
            .unwrap_or((None, None));

        StandardTrackInfo {
            title: umg_track.title.clone(),
            artist_name,
            track_number: umg_track.track_number as u32,
            disc_number: Some(umg_track.disc_number as u32),
            duration_seconds: umg_track.duration as u32,
            isrc: if umg_track.isrc.is_empty() { None } else { Some(umg_track.isrc.clone()) },
            genre: umg_track.genre.clone(),
            composer,
            lyricist,
            producer,
            copyright: umg_track.p_line.clone(),
            audio_resource_id: Some(umg_track.resource_reference.clone()),
            bitrate,
            sample_rate,
            provider_metadata,
        }
    }).collect()
}

/// 转换音频资源
fn convert_audio_resources(umg_resources: &[AudioResource]) -> Vec<StandardAudioResource> {
    umg_resources.iter().map(|umg_resource| {
        // 从文件名推断音频格式
        let format = match umg_resource.file_name.split('.').last().unwrap_or("").to_lowercase().as_str() {
            "mp3" => AudioFormat::Mp3,
            "flac" => AudioFormat::Flac,
            "wav" => AudioFormat::Wav,
            "aac" => AudioFormat::Aac,
            "m4a" => AudioFormat::M4a,
            "ogg" => AudioFormat::Ogg,
            ext => AudioFormat::Unknown(ext.to_string()),
        };

        StandardAudioResource {
            resource_id: umg_resource.resource_id.clone(),
            local_path: umg_resource.file_path.clone(),
            remote_path: None,
            file_name: umg_resource.file_name.clone(),
            file_size: umg_resource.file_size,
            format,
            bitrate: umg_resource.bit_rate,
            sample_rate: umg_resource.sample_rate.as_ref().and_then(|s| {
                // UMG采样率可能是浮点数（如"44.1"），需要特殊处理
                if let Ok(f_val) = s.parse::<f32>() {
                    // 乘以10保持一位小数精度：44.1 -> 441
                    Some((f_val * 10.0) as u32)
                } else {
                    s.parse::<u32>().ok()
                }
            }),
            channels: None,
            duration_seconds: Some(umg_resource.duration as u32),
            checksum: Some(umg_resource.md5_hash.clone()),
            uploaded: false,
            uploaded_at: None,
        }
    }).collect()
}

/// 转换图片资源
fn convert_image_resources(umg_resources: &[ImageResource]) -> Vec<StandardImageResource> {
    umg_resources.iter().map(|umg_resource| {
        // 从文件名推断图片格式
        let format = match umg_resource.file_name.split('.').last().unwrap_or("").to_lowercase().as_str() {
            "jpg" | "jpeg" => ImageFormat::Jpeg,
            "png" => ImageFormat::Png,
            "gif" => ImageFormat::Gif,
            "bmp" => ImageFormat::Bmp,
            "webp" => ImageFormat::Webp,
            ext => ImageFormat::Unknown(ext.to_string()),
        };

        // 从类型字符串推断图片类型
        let image_type = match umg_resource.image_type.to_lowercase().as_str() {
            "albumcover" | "cover" => ImageType::AlbumCover,
            "artistphoto" | "artist" => ImageType::ArtistPhoto,
            "background" => ImageType::Background,
            _ => ImageType::Other,
        };

        StandardImageResource {
            resource_id: umg_resource.resource_id.clone(),
            local_path: umg_resource.file_path.clone(),
            remote_path: None,
            file_name: umg_resource.file_name.clone(),
            file_size: umg_resource.file_size,
            image_type,
            format,
            width: umg_resource.width,
            height: umg_resource.height,
            checksum: Some(umg_resource.md5_hash.clone()),
            uploaded: false,
            uploaded_at: None,
        }
    }).collect()
}

/// 创建 UMG 特定的元数据
fn create_umg_metadata(party_list: &[PartyInfo]) -> std::collections::HashMap<String, String> {
    let mut metadata = std::collections::HashMap::new();
    
    metadata.insert("provider".to_string(), "UMG".to_string());
    metadata.insert("party_count".to_string(), party_list.len().to_string());
    
    // 添加主要参与方信息
    for (index, party) in party_list.iter().enumerate() {
        let prefix = format!("party_{}", index);
        metadata.insert(format!("{}_id", prefix), party.party_id.clone());
        metadata.insert(format!("{}_name", prefix), party.party_name.clone());
        metadata.insert(format!("{}_type", prefix), party.party_type.clone());
        
        if !party.roles.is_empty() {
            metadata.insert(format!("{}_roles", prefix), party.roles.join(","));
        }
    }
    
    metadata
}

/// 验证 UMG 解析结果的完整性
pub fn validate_umg_parsed_release(parsed: &ParsedRelease) -> Result<()> {
    // 验证基本信息
    if parsed.album_info.grid.is_empty() {
        return Err(anyhow::anyhow!("专辑 GRid 不能为空"));
    }
    
    if parsed.album_info.title.is_empty() {
        return Err(anyhow::anyhow!("专辑标题不能为空"));
    }
    
    // 验证曲目信息
    for (index, track) in parsed.tracks.iter().enumerate() {
        if track.title.is_empty() {
            return Err(anyhow::anyhow!("曲目 {} 标题不能为空", index + 1));
        }
        
        if track.resource_reference.is_empty() {
            return Err(anyhow::anyhow!("曲目 {} 资源引用不能为空", index + 1));
        }
        
        if track.track_number <= 0 {
            return Err(anyhow::anyhow!("曲目 {} 序号必须大于 0", index + 1));
        }
    }
    
    // 验证资源完整性
    let track_resource_refs: std::collections::HashSet<_> = parsed.tracks
        .iter()
        .map(|t| &t.resource_reference)
        .collect();
    
    let audio_resource_ids: std::collections::HashSet<_> = parsed.audio_resources
        .iter()
        .map(|r| &r.resource_id)
        .collect();
    
    for track_ref in &track_resource_refs {
        if !audio_resource_ids.contains(track_ref) {
            return Err(anyhow::anyhow!("曲目引用的音频资源 {} 不存在", track_ref));
        }
    }
    
    debug!("UMG 解析结果验证通过");
    Ok(())
}
