//! 状态跟踪器
//! 
//! 负责跟踪文件解析状态，支持断点续传

use anyhow::Result;
use log::{info, debug, warn};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;

use chrono::{DateTime, Utc};

use crate::importers::models::ParseStatus;

/// 本地状态文件结构（兼容原有 Sony 导入器）
#[derive(Debug, Clone, Serialize, Deserialize)]
struct LocalStatus {
    /// 文件路径
    pub file_path: String,
    /// 处理状态
    pub status: ParseStatus,
    /// 文件哈希
    pub file_hash: String,
    /// 开始时间
    pub start_time: Option<DateTime<Utc>>,
    /// 结束时间
    pub end_time: Option<DateTime<Utc>>,
    /// 错误信息
    pub error_message: Option<String>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 状态跟踪器（使用本地状态文件系统）
pub struct StatusTracker {
    root_folder: String,
    status_cache: HashMap<String, FileStatus>,
}

/// 文件状态信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileStatus {
    /// 文件路径
    pub file_path: String,
    /// 解析状态
    pub status: ParseStatus,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
    /// 错误信息
    pub error_message: Option<String>,
    /// 重试次数
    pub retry_count: u32,
    /// 文件大小
    pub file_size: Option<u64>,
    /// 文件修改时间
    pub file_modified: Option<DateTime<Utc>>,
}

impl StatusTracker {
    /// 创建新的状态跟踪器
    pub async fn new(_status_dir: &str, root_folder: &str) -> Result<Self> {
        info!("初始化本地状态跟踪器: {}", root_folder);

        let mut tracker = Self {
            root_folder: root_folder.to_string(),
            status_cache: HashMap::new(),
        };
        
        // 加载现有状态
        tracker.load_status_cache().await?;
        
        info!("状态跟踪器初始化完成，加载了 {} 个文件状态", tracker.status_cache.len());
        Ok(tracker)
    }
    
    /// 检查文件是否已成功解析
    pub async fn is_successfully_parsed(&self, file_path: &str) -> Result<bool> {
        let relative_path = self.get_relative_path(file_path);
        
        if let Some(status) = self.status_cache.get(&relative_path) {
            // 检查文件是否已成功解析且文件未被修改
            if status.status == ParseStatus::Complete {
                // 检查文件是否被修改
                if let Ok(modified) = self.get_file_modified_time(file_path).await {
                    if let Some(cached_modified) = status.file_modified {
                        if modified <= cached_modified {
                            debug!("文件已成功解析且未被修改: {}", file_path);
                            return Ok(true);
                        } else {
                            debug!("文件已被修改，需要重新解析: {}", file_path);
                        }
                    }
                }
            }
        }
        
        Ok(false)
    }
    
    /// 标记开始解析
    pub async fn mark_parsing_start(&mut self, file_path: &str) -> Result<()> {
        debug!("标记开始解析: {}", file_path);
        
        let relative_path = self.get_relative_path(file_path);
        let file_size = self.get_file_size(file_path).await.ok();
        let file_modified = self.get_file_modified_time(file_path).await.ok();
        
        let status = FileStatus {
            file_path: relative_path.clone(),
            status: ParseStatus::Parsing,
            last_updated: Utc::now(),
            error_message: None,
            retry_count: self.get_retry_count(&relative_path),
            file_size,
            file_modified,
        };
        
        self.status_cache.insert(relative_path.clone(), status);
        self.save_status(&relative_path).await?;
        
        Ok(())
    }
    
    /// 标记解析成功
    pub async fn mark_parsing_success(&mut self, file_path: &str) -> Result<()> {
        debug!("标记解析成功: {}", file_path);
        
        let relative_path = self.get_relative_path(file_path);
        
        if let Some(status) = self.status_cache.get_mut(&relative_path) {
            status.status = ParseStatus::Complete;
            status.last_updated = Utc::now();
            status.error_message = None;
        }
        
        self.save_status(&relative_path).await?;
        Ok(())
    }
    
    /// 标记解析失败
    pub async fn mark_parsing_failed(&mut self, file_path: &str, error: &str) -> Result<()> {
        debug!("标记解析失败: {} - {}", file_path, error);
        
        let relative_path = self.get_relative_path(file_path);
        
        if let Some(status) = self.status_cache.get_mut(&relative_path) {
            status.status = ParseStatus::Failed;
            status.last_updated = Utc::now();
            status.error_message = Some(error.to_string());
            status.retry_count += 1;
        }
        
        self.save_status(&relative_path).await?;
        Ok(())
    }
    
    /// 获取文件状态
    pub fn get_file_status(&self, file_path: &str) -> Option<&FileStatus> {
        let relative_path = self.get_relative_path(file_path);
        self.status_cache.get(&relative_path)
    }
    
    /// 获取所有状态
    pub fn get_all_status(&self) -> &HashMap<String, FileStatus> {
        &self.status_cache
    }
    
    /// 获取状态统计
    pub fn get_status_statistics(&self) -> StatusStatistics {
        let mut stats = StatusStatistics::default();
        
        for status in self.status_cache.values() {
            match status.status {
                ParseStatus::NotStarted => stats.not_started += 1,
                ParseStatus::Parsing => stats.parsing += 1,
                ParseStatus::ParseSuccess => stats.parse_success += 1,
                ParseStatus::ParseFailed => stats.parse_failed += 1,
                ParseStatus::Uploading => stats.uploading += 1,
                ParseStatus::UploadSuccess => stats.upload_success += 1,
                ParseStatus::UploadFailed => stats.upload_failed += 1,
                ParseStatus::DatabaseSaving => stats.database_saving += 1,
                ParseStatus::Complete => stats.complete += 1,
                ParseStatus::Failed => stats.failed += 1,
            }
        }
        
        stats.total = self.status_cache.len();
        stats
    }
    

    
    /// 清理资源
    pub fn cleanup(&mut self) -> Result<()> {
        info!("清理状态跟踪器资源");
        self.status_cache.clear();
        Ok(())
    }
    
    // 私有方法
    
    /// 获取相对路径
    fn get_relative_path(&self, file_path: &str) -> String {
        if let Ok(stripped) = Path::new(file_path).strip_prefix(&self.root_folder) {
            stripped.to_string_lossy().to_string()
        } else {
            file_path.to_string()
        }
    }
    
    /// 获取重试次数
    fn get_retry_count(&self, relative_path: &str) -> u32 {
        self.status_cache
            .get(relative_path)
            .map(|s| s.retry_count)
            .unwrap_or(0)
    }
    
    /// 获取文件大小
    async fn get_file_size(&self, file_path: &str) -> Result<u64> {
        let metadata = fs::metadata(file_path).await?;
        Ok(metadata.len())
    }
    
    /// 获取文件修改时间
    async fn get_file_modified_time(&self, file_path: &str) -> Result<DateTime<Utc>> {
        let metadata = fs::metadata(file_path).await?;
        let modified = metadata.modified()?;
        Ok(DateTime::from(modified))
    }
    
    /// 加载状态缓存（本地状态文件模式）
    async fn load_status_cache(&mut self) -> Result<()> {
        debug!("加载本地状态缓存");

        // 扫描根目录下的所有 .parse_status.json 文件
        self.scan_local_status_files(&self.root_folder.clone()).await?;

        Ok(())
    }

    /// 扫描本地状态文件
    async fn scan_local_status_files(&mut self, dir: &str) -> Result<()> {
        use walkdir::WalkDir;

        for entry in WalkDir::new(dir) {
            let entry = entry?;
            if entry.file_type().is_file() {
                let path = entry.path();
                if path.file_name().map_or(false, |name| name == ".parse_status.json") {
                    match self.load_local_status_file(path).await {
                        Ok((relative_path, status)) => {
                            self.status_cache.insert(relative_path, status);
                        }
                        Err(e) => {
                            warn!("加载本地状态文件失败 {}: {}", path.display(), e);
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// 加载本地状态文件
    async fn load_local_status_file(&self, path: &Path) -> Result<(String, FileStatus)> {
        let content = fs::read_to_string(path).await?;
        let local_status: LocalStatus = serde_json::from_str(&content)?;

        // 转换为 FileStatus
        let status = FileStatus {
            file_path: local_status.file_path.clone(),
            status: local_status.status,
            last_updated: local_status.updated_at,
            error_message: local_status.error_message,
            retry_count: 0,
            file_size: None,
            file_modified: None,
        };

        Ok((local_status.file_path, status))
    }
    
    /// 保存状态（本地状态文件模式）
    async fn save_status(&self, relative_path: &str) -> Result<()> {
        if let Some(status) = self.status_cache.get(relative_path) {
            // 使用本地状态文件模式（与原有 Sony 导入器兼容）
            let file_path = format!("{}/{}", self.root_folder, relative_path);
            let dir_path = Path::new(&file_path).parent()
                .ok_or_else(|| anyhow::anyhow!("Invalid file path: {}", file_path))?;
            let status_file = dir_path.join(".parse_status.json");

            // 转换为本地状态格式
            let local_status = LocalStatus {
                file_path: relative_path.to_string(),
                status: status.status.clone(),
                file_hash: String::new(), // 暂时不使用哈希
                start_time: None,
                end_time: None,
                error_message: status.error_message.clone(),
                created_at: status.last_updated,
                updated_at: status.last_updated,
            };

            let content = serde_json::to_string_pretty(&local_status)?;
            fs::write(&status_file, content).await?;
        }

        Ok(())
    }

}

/// 状态统计信息
#[derive(Debug, Default)]
pub struct StatusStatistics {
    pub total: usize,
    pub not_started: usize,
    pub parsing: usize,
    pub parse_success: usize,
    pub parse_failed: usize,
    pub uploading: usize,
    pub upload_success: usize,
    pub upload_failed: usize,
    pub database_saving: usize,
    pub complete: usize,
    pub failed: usize,
}

impl StatusStatistics {
    /// 生成统计报告
    pub fn generate_report(&self) -> String {
        format!(
            "状态统计:\n\
             - 总计: {}\n\
             - 未开始: {}\n\
             - 解析中: {}\n\
             - 解析成功: {}\n\
             - 解析失败: {}\n\
             - 上传中: {}\n\
             - 上传成功: {}\n\
             - 上传失败: {}\n\
             - 数据库保存中: {}\n\
             - 完成: {}\n\
             - 失败: {}",
            self.total,
            self.not_started,
            self.parsing,
            self.parse_success,
            self.parse_failed,
            self.uploading,
            self.upload_success,
            self.upload_failed,
            self.database_saving,
            self.complete,
            self.failed
        )
    }
}
