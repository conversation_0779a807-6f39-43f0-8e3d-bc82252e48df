//! 导入日志记录器
//! 
//! 负责记录导入过程中的详细日志信息

use anyhow::Result;
use log::{info, debug};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::io::Write;
use tokio::fs;

use chrono::{DateTime, Utc, Local};

/// 每日记录（JSONL格式）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DailyRecord {
    /// 文件路径
    pub file_path: String,
    /// 开始时间
    pub start_time: DateTime<Utc>,
    /// 结束时间
    pub end_time: Option<DateTime<Utc>>,
    /// 处理结果
    pub result: DailyProcessResult,
    /// 专辑标题
    pub album_title: Option<String>,
    /// GRid
    pub grid: Option<String>,
    /// 曲目数量
    pub track_count: Option<usize>,
    /// 音频资源数量
    pub audio_resource_count: Option<usize>,
    /// 图片资源数量
    pub image_resource_count: Option<usize>,
    /// NAS文件路径列表
    pub nas_file_paths: Vec<String>,
    /// 错误信息
    pub error_message: Option<String>,
    /// 处理时长（毫秒）
    pub duration_ms: Option<i64>,
}

/// 每日处理结果
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DailyProcessResult {
    /// 解析成功
    ParseSuccess,
    /// 解析失败
    ParseFailed,
    /// 上传成功
    UploadSuccess,
    /// 上传失败
    UploadFailed,
    /// 数据库保存成功
    DatabaseSuccess,
    /// 数据库保存失败
    DatabaseFailed,
    /// 跳过处理
    Skipped,
}

/// 导入日志记录器
pub struct ImportLogger {
    log_dir: String,
    current_session: LogSession,
    processing_records: HashMap<String, ProcessingRecord>,
    /// 每日日志记录（JSONL格式）
    daily_records: HashMap<String, DailyRecord>,
}

/// 日志会话
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogSession {
    /// 会话 ID
    pub session_id: String,
    /// 开始时间
    pub started_at: DateTime<Utc>,
    /// 结束时间
    pub ended_at: Option<DateTime<Utc>>,
    /// 处理的文件数量
    pub files_processed: usize,
    /// 成功数量
    pub success_count: usize,
    /// 失败数量
    pub failure_count: usize,
}

/// 处理记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingRecord {
    /// 文件路径
    pub file_path: String,
    /// 开始时间
    pub started_at: DateTime<Utc>,
    /// 结束时间
    pub ended_at: Option<DateTime<Utc>>,
    /// 处理阶段
    pub stages: Vec<ProcessingStage>,
    /// 最终状态
    pub final_status: ProcessingStatus,
    /// 错误信息
    pub error_message: Option<String>,
}

/// 处理阶段
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingStage {
    /// 阶段名称
    pub stage_name: String,
    /// 开始时间
    pub started_at: DateTime<Utc>,
    /// 结束时间
    pub ended_at: Option<DateTime<Utc>>,
    /// 是否成功
    pub success: bool,
    /// 详细信息
    pub details: Option<String>,
}

/// 处理状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ProcessingStatus {
    /// 进行中
    InProgress,
    /// 成功
    Success,
    /// 失败
    Failed,
    /// 跳过
    Skipped,
}

impl ImportLogger {
    /// 创建新的导入日志记录器
    pub fn new(log_dir: &str) -> Result<Self> {
        info!("初始化导入日志记录器: {}", log_dir);
        
        // 确保日志目录存在
        std::fs::create_dir_all(log_dir)?;
        
        let session_id = format!("session_{}", Utc::now().format("%Y%m%d_%H%M%S"));
        let current_session = LogSession {
            session_id,
            started_at: Utc::now(),
            ended_at: None,
            files_processed: 0,
            success_count: 0,
            failure_count: 0,
        };
        
        let logger = Self {
            log_dir: log_dir.to_string(),
            current_session,
            processing_records: HashMap::new(),
            daily_records: HashMap::new(),
        };
        
        info!("导入日志记录器初始化完成，会话 ID: {}", logger.current_session.session_id);
        Ok(logger)
    }
    
    /// 开始处理文件
    pub fn start_processing(&mut self, file_path: &str) -> Result<()> {
        debug!("开始处理文件: {}", file_path);

        let record = ProcessingRecord {
            file_path: file_path.to_string(),
            started_at: Utc::now(),
            ended_at: None,
            stages: Vec::new(),
            final_status: ProcessingStatus::InProgress,
            error_message: None,
        };

        // 创建每日记录
        let daily_record = DailyRecord {
            file_path: file_path.to_string(),
            start_time: Utc::now(),
            end_time: None,
            result: DailyProcessResult::ParseSuccess, // 临时状态
            album_title: None,
            grid: None,
            track_count: None,
            audio_resource_count: None,
            image_resource_count: None,
            nas_file_paths: Vec::new(),
            error_message: None,
            duration_ms: None,
        };

        self.processing_records.insert(file_path.to_string(), record);
        self.daily_records.insert(file_path.to_string(), daily_record);
        self.current_session.files_processed += 1;

        Ok(())
    }
    
    /// 记录处理阶段
    pub fn record_stage(&mut self, file_path: &str, stage_name: &str, success: bool, details: Option<String>) -> Result<()> {
        debug!("记录处理阶段: {} - {} - {}", file_path, stage_name, if success { "成功" } else { "失败" });
        
        if let Some(record) = self.processing_records.get_mut(file_path) {
            let stage = ProcessingStage {
                stage_name: stage_name.to_string(),
                started_at: Utc::now(),
                ended_at: Some(Utc::now()),
                success,
                details,
            };
            
            record.stages.push(stage);
        }
        
        Ok(())
    }
    
    /// 记录解析失败
    pub fn record_parse_failure(&mut self, file_path: &str, error: &str) -> Result<()> {
        self.record_stage(file_path, "XML解析", false, Some(error.to_string()))?;

        if let Some(record) = self.processing_records.get_mut(file_path) {
            record.final_status = ProcessingStatus::Failed;
            record.error_message = Some(error.to_string());
        }

        Ok(())
    }

    /// 记录解析成功（与原有 Sony 导入器兼容）
    pub fn record_parse_success(&mut self, file_path: &str, details: Option<String>) -> Result<()> {
        self.record_stage(file_path, "XML解析", true, details)
    }

    /// 记录上传成功
    pub fn record_upload_success(&mut self, file_path: &str, details: Option<String>) -> Result<()> {
        self.record_stage(file_path, "文件上传", true, details)
    }

    /// 获取摘要记录（兼容旧接口）
    pub fn get_summary_records(&self) -> HashMap<String, Vec<String>> {
        let mut records = HashMap::new();
        records.insert("success".to_string(), Vec::new());
        records.insert("failure".to_string(), Vec::new());
        records.insert("skipped".to_string(), Vec::new());

        for (file_path, record) in &self.processing_records {
            match record.final_status {
                ProcessingStatus::Success => {
                    records.get_mut("success").unwrap().push(file_path.clone());
                }
                ProcessingStatus::Failed => {
                    records.get_mut("failure").unwrap().push(file_path.clone());
                }
                ProcessingStatus::Skipped => {
                    records.get_mut("skipped").unwrap().push(file_path.clone());
                }
                _ => {} // InProgress 状态不计入
            }
        }

        records
    }

    /// 记录上传失败
    pub fn record_upload_failure(&mut self, file_path: &str, error: &str) -> Result<()> {
        self.record_stage(file_path, "文件上传", false, Some(error.to_string()))?;

        if let Some(record) = self.processing_records.get_mut(file_path) {
            record.final_status = ProcessingStatus::Failed;
            record.error_message = Some(error.to_string());
        }

        Ok(())
    }

    /// 记录数据库保存成功
    pub fn record_database_success(&mut self, file_path: &str, details: Option<String>) -> Result<()> {
        self.record_stage(file_path, "数据库保存", true, details)
    }

    /// 记录数据库保存失败
    pub fn record_database_failure(&mut self, file_path: &str, error: &str) -> Result<()> {
        self.record_stage(file_path, "数据库保存", false, Some(error.to_string()))?;

        if let Some(record) = self.processing_records.get_mut(file_path) {
            record.final_status = ProcessingStatus::Failed;
            record.error_message = Some(error.to_string());
        }

        Ok(())
    }
    
    /// 记录成功
    pub fn record_success(&mut self, file_path: &str) -> Result<()> {
        debug!("记录处理成功: {}", file_path);
        
        if let Some(record) = self.processing_records.get_mut(file_path) {
            record.final_status = ProcessingStatus::Success;
            record.error_message = None;
        }
        
        self.current_session.success_count += 1;
        Ok(())
    }
    
    /// 完成文件处理
    pub fn finish_processing(&mut self, file_path: &str) -> Result<()> {
        debug!("完成文件处理: {}", file_path);

        if let Some(record) = self.processing_records.get_mut(file_path) {
            record.ended_at = Some(Utc::now());

            if record.final_status == ProcessingStatus::Failed {
                self.current_session.failure_count += 1;
            }
        }

        // 完成每日记录并写入文件
        if let Some(daily_record) = self.daily_records.get_mut(file_path) {
            daily_record.end_time = Some(Utc::now());
            if let Some(start_time) = daily_record.start_time.timestamp_millis().into() {
                if let Some(end_time) = daily_record.end_time.as_ref().map(|t| t.timestamp_millis()) {
                    daily_record.duration_ms = Some(end_time - start_time);
                }
            }

            // 克隆记录以避免借用冲突
            let record_to_write = daily_record.clone();
            self.write_daily_record_to_file(&record_to_write)?;
        }

        Ok(())
    }

    /// 记录解析成功（每日日志）
    pub fn record_daily_parse_success(&mut self, file_path: &str, album_title: &str, grid: &str, track_count: usize, audio_count: usize, image_count: usize) -> Result<()> {
        if let Some(daily_record) = self.daily_records.get_mut(file_path) {
            daily_record.result = DailyProcessResult::ParseSuccess;
            daily_record.album_title = Some(album_title.to_string());
            daily_record.grid = Some(grid.to_string());
            daily_record.track_count = Some(track_count);
            daily_record.audio_resource_count = Some(audio_count);
            daily_record.image_resource_count = Some(image_count);
        }
        Ok(())
    }

    /// 记录上传成功（每日日志）
    pub fn record_daily_upload_success(&mut self, file_path: &str, nas_paths: Vec<String>) -> Result<()> {
        if let Some(daily_record) = self.daily_records.get_mut(file_path) {
            daily_record.result = DailyProcessResult::UploadSuccess;
            daily_record.nas_file_paths = nas_paths;
        }
        Ok(())
    }

    /// 记录数据库保存成功（每日日志）
    pub fn record_daily_database_success(&mut self, file_path: &str) -> Result<()> {
        if let Some(daily_record) = self.daily_records.get_mut(file_path) {
            daily_record.result = DailyProcessResult::DatabaseSuccess;
        }
        Ok(())
    }

    /// 记录处理失败（每日日志）
    pub fn record_daily_failure(&mut self, file_path: &str, result: DailyProcessResult, error: &str) -> Result<()> {
        if let Some(daily_record) = self.daily_records.get_mut(file_path) {
            daily_record.result = result;
            daily_record.error_message = Some(error.to_string());
        }
        Ok(())
    }
    
    /// 生成日志报告
    pub async fn generate_report(&mut self) -> Result<String> {
        info!("生成日志报告");
        
        // 结束当前会话
        self.current_session.ended_at = Some(Utc::now());
        
        let report = self.create_report_content();
        
        // 保存报告到文件
        let report_file = format!("{}/report_{}.txt", 
                                 self.log_dir, 
                                 self.current_session.session_id);
        
        fs::write(&report_file, &report).await?;
        info!("日志报告已保存: {}", report_file);
        
        // 保存详细记录
        self.save_detailed_records().await?;
        
        Ok(report)
    }
    
    /// 获取会话统计
    pub fn get_session_statistics(&self) -> SessionStatistics {
        let mut stats = SessionStatistics {
            session_id: self.current_session.session_id.clone(),
            started_at: self.current_session.started_at,
            ended_at: self.current_session.ended_at,
            total_files: self.current_session.files_processed,
            success_count: self.current_session.success_count,
            failure_count: self.current_session.failure_count,
            in_progress_count: 0,
            skipped_count: 0,
            average_processing_time: 0.0,
        };
        
        let mut total_duration = 0.0;
        let mut completed_count = 0;
        
        for record in self.processing_records.values() {
            match record.final_status {
                ProcessingStatus::InProgress => stats.in_progress_count += 1,
                ProcessingStatus::Skipped => stats.skipped_count += 1,
                _ => {}
            }
            
            if let (Some(ended), started) = (record.ended_at, record.started_at) {
                let duration = (ended - started).num_milliseconds() as f64 / 1000.0;
                total_duration += duration;
                completed_count += 1;
            }
        }
        
        if completed_count > 0 {
            stats.average_processing_time = total_duration / completed_count as f64;
        }
        
        stats
    }
    
    /// 清理资源
    pub fn cleanup(&mut self) -> Result<()> {
        info!("清理导入日志记录器资源");
        
        // 如果会话还没结束，先结束它
        if self.current_session.ended_at.is_none() {
            self.current_session.ended_at = Some(Utc::now());
        }
        
        self.processing_records.clear();
        Ok(())
    }
    
    // 私有方法
    
    /// 创建报告内容
    fn create_report_content(&self) -> String {
        let stats = self.get_session_statistics();
        let duration = if let Some(ended) = stats.ended_at {
            (ended - stats.started_at).num_seconds()
        } else {
            (Utc::now() - stats.started_at).num_seconds()
        };
        
        let mut report = format!(
            "音乐导入处理报告\n\
             ==================\n\
             会话 ID: {}\n\
             开始时间: {}\n\
             结束时间: {}\n\
             处理时长: {} 秒\n\
             \n\
             处理统计:\n\
             - 总文件数: {}\n\
             - 成功: {}\n\
             - 失败: {}\n\
             - 进行中: {}\n\
             - 跳过: {}\n\
             - 平均处理时间: {:.2} 秒\n\
             \n",
            stats.session_id,
            stats.started_at.with_timezone(&Local).format("%Y-%m-%d %H:%M:%S"),
            stats.ended_at.map(|t| t.with_timezone(&Local).format("%Y-%m-%d %H:%M:%S").to_string())
                .unwrap_or_else(|| "进行中".to_string()),
            duration,
            stats.total_files,
            stats.success_count,
            stats.failure_count,
            stats.in_progress_count,
            stats.skipped_count,
            stats.average_processing_time
        );
        
        // 添加失败文件详情
        let failed_files: Vec<_> = self.processing_records
            .values()
            .filter(|r| r.final_status == ProcessingStatus::Failed)
            .collect();
        
        if !failed_files.is_empty() {
            report.push_str("失败文件详情:\n");
            report.push_str("================\n");
            
            for record in failed_files {
                report.push_str(&format!(
                    "文件: {}\n错误: {}\n\n",
                    record.file_path,
                    record.error_message.as_deref().unwrap_or("未知错误")
                ));
            }
        }
        
        report
    }
    
    /// 保存详细记录
    async fn save_detailed_records(&self) -> Result<()> {
        let records_file = format!("{}/records_{}.json", 
                                  self.log_dir, 
                                  self.current_session.session_id);
        
        let data = serde_json::json!({
            "session": self.current_session,
            "records": self.processing_records
        });
        
        let content = serde_json::to_string_pretty(&data)?;
        fs::write(&records_file, content).await?;
        
        debug!("详细记录已保存: {}", records_file);
        Ok(())
    }

    /// 写入每日记录到JSONL文件
    fn write_daily_record_to_file(&self, record: &DailyRecord) -> Result<()> {
        use std::fs::OpenOptions;

        // 生成每日日志文件名
        let current_date = Local::now().format("%Y-%m-%d").to_string();
        let daily_log_file = format!("{}/daily_process_{}.jsonl", self.log_dir, current_date);

        // 序列化记录为JSON
        let json_line = serde_json::to_string(record)?;

        // 追加写入文件
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&daily_log_file)?;

        writeln!(file, "{}", json_line)?;

        debug!("写入每日日志记录到: {}", daily_log_file);
        Ok(())
    }
}

/// 会话统计信息
#[derive(Debug)]
pub struct SessionStatistics {
    pub session_id: String,
    pub started_at: DateTime<Utc>,
    pub ended_at: Option<DateTime<Utc>>,
    pub total_files: usize,
    pub success_count: usize,
    pub failure_count: usize,
    pub in_progress_count: usize,
    pub skipped_count: usize,
    pub average_processing_time: f64,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_processing_status() {
        assert_eq!(ProcessingStatus::Success, ProcessingStatus::Success);
        assert_ne!(ProcessingStatus::Success, ProcessingStatus::Failed);
    }
    
    #[tokio::test]
    async fn test_logger_creation() {
        let temp_dir = std::env::temp_dir().join("test_logger");
        let logger = ImportLogger::new(temp_dir.to_str().unwrap());
        
        assert!(logger.is_ok());
        
        let logger = logger.unwrap();
        assert!(!logger.current_session.session_id.is_empty());
        assert_eq!(logger.current_session.files_processed, 0);
        
        // 清理测试目录
        let _ = std::fs::remove_dir_all(&temp_dir);
    }
}
