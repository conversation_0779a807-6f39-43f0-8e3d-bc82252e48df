use anyhow::Result;
use std::collections::HashMap;

pub mod base;
pub mod sony;
pub mod warner;
pub mod umg;

// 新的重构模块
pub mod models;
pub mod parsers;
pub mod engine;
pub mod unified_importer;

pub use base::BaseImporter;
pub use unified_importer::{UnifiedImporter, ImporterFactory};

#[derive(Debug)]
pub enum ImportResult {
    Success,
    Failure(String),
}

pub trait Importer {
    fn start_import(&mut self) -> ImportResult;
    fn send_email(&self) -> Result<()>;
    fn get_records(&self) -> HashMap<String, Vec<String>>;
    fn cleanup(&mut self) {}
}


