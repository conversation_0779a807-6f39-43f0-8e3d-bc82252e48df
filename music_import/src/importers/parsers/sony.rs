//! Sony DDEX 解析器
//! 
//! 实现 Sony Music 的 DDEX ERN XML 解析功能

use anyhow::Result;
use async_trait::async_trait;
use log::{info, debug, warn};

use super::{Parser, SupportedExtensions};
use crate::importers::models::*;
use crate::importers::sony::ddex_parser::DdexParser;

/// Sony DDEX 解析器
pub struct SonyParser {
    ddex_parser: DdexParser,
}

impl SonyParser {
    /// 创建新的 Sony 解析器
    pub fn new() -> Self {
        Self {
            ddex_parser: DdexParser::new(),
        }
    }
}

#[async_trait]
impl Parser for SonyParser {
    fn get_provider_name(&self) -> &str {
        "Sony"
    }
    
    fn validate_xml(&self, xml_path: &str) -> Result<()> {
        // 使用现有的 DDEX 解析器验证
        debug!("Validating Sony DDEX XML: {}", xml_path);
        
        // 检查文件是否存在
        if !std::path::Path::new(xml_path).exists() {
            return Err(anyhow::anyhow!("XML file does not exist: {}", xml_path));
        }
        
        // 读取文件内容进行基本验证
        let content = std::fs::read_to_string(xml_path)?;
        
        // 检查是否为有效的 DDEX XML
        if !content.contains("NewReleaseMessage") {
            return Err(anyhow::anyhow!("Not a valid DDEX NewReleaseMessage XML"));
        }
        
        if !content.contains("http://ddex.net/xml/ern/") {
            return Err(anyhow::anyhow!("Missing DDEX ERN namespace"));
        }
        
        info!("Sony DDEX XML validation passed: {}", xml_path);
        Ok(())
    }
    
    async fn parse_xml(&self, xml_path: &str, base_dir: &str) -> Result<ParsedRelease> {
        info!("Parsing Sony DDEX XML: {}", xml_path);
        
        // 使用现有的 DDEX 解析器
        let sony_parsed = self.ddex_parser.parse_xml(xml_path, base_dir)?;
        
        // 转换为标准化的 ParsedRelease 格式
        let parsed_release = self.convert_sony_to_standard(sony_parsed, xml_path)?;
        
        info!("Sony DDEX XML parsing completed: {} tracks, {} audio resources, {} image resources",
              parsed_release.tracks.len(),
              parsed_release.audio_resources.len(),
              parsed_release.image_resources.len());
        
        Ok(parsed_release)
    }
    
    fn get_supported_extensions(&self) -> SupportedExtensions {
        SupportedExtensions {
            audio: vec![
                "mp3".to_string(),
                "flac".to_string(),
                "wav".to_string(),
                "aac".to_string(),
                "m4a".to_string(),
            ],
            image: vec![
                "jpg".to_string(),
                "jpeg".to_string(),
                "png".to_string(),
                "gif".to_string(),
                "bmp".to_string(),
            ],
        }
    }
    
    fn generate_remote_path(&self, local_path: &str, date: &str) -> String {
        // Sony 的远程路径格式应该使用 PathGenerator 来生成正确的路径
        // 这里提供一个简化版本，实际应该通过 PathGenerator 处理
        let filename = std::path::Path::new(local_path)
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown");

        // 注意：这个方法主要用于测试，实际路径生成应该使用 PathGenerator
        // 格式：SONY/YYYYMMDD/AlbumID_Timestamp/FileID.extension
        warn!("Sony 解析器应该使用 PathGenerator 来生成正确的路径格式");
        format!("Sony/{}/{}", date, filename)
    }
    
    async fn preprocess_file(&self, xml_path: &str) -> Result<()> {
        debug!("Preprocessing Sony DDEX file: {}", xml_path);
        
        // Sony 特定的预处理逻辑
        // 例如：检查编码、验证 GRID 等
        
        Ok(())
    }
    
    async fn postprocess_result(&self, result: &mut ParsedRelease) -> Result<()> {
        debug!("Postprocessing Sony DDEX result");
        
        // Sony 特定的后处理逻辑
        // 例如：验证 ISRC、补充缺失信息等
        
        // 验证所有曲目都有 ISRC
        for track in &mut result.tracks {
            if track.isrc.is_none() {
                warn!("Track '{}' missing ISRC code", track.title);
            }
        }
        
        Ok(())
    }
}

impl SonyParser {
    /// 将 Sony 特定的解析结果转换为标准格式
    fn convert_sony_to_standard(
        &self,
        sony_parsed: crate::importers::sony::ParsedRelease,
        xml_path: &str,
    ) -> Result<ParsedRelease> {
        use chrono::Utc;
        use std::collections::HashMap;
        
        // 转换消息头
        let message_header = MessageHeader {
            message_thread_id: sony_parsed.message_header.message_thread_id,
            message_id: sony_parsed.message_header.message_id,
            sender: PartyInfo {
                party_id: sony_parsed.message_header.sender.party_id,
                party_name: sony_parsed.message_header.sender.party_name.clone(),
                party_type: sony_parsed.message_header.sender.party_name, // Sony 结构中没有 party_type，使用 party_name
            },
            recipient: PartyInfo {
                party_id: sony_parsed.message_header.recipient.party_id,
                party_name: sony_parsed.message_header.recipient.party_name.clone(),
                party_type: sony_parsed.message_header.recipient.party_name, // Sony 结构中没有 party_type，使用 party_name
            },
            created_datetime: sony_parsed.message_header.created_datetime,
            control_type: sony_parsed.message_header.control_type,
        };
        
        // 转换专辑信息
        let album_info = AlbumInfo {
            title: sony_parsed.album_info.title,
            artist_name: sony_parsed.album_info.display_artist_name,
            release_date: sony_parsed.album_info.release_date.unwrap_or_else(|| "Unknown".to_string()),
            album_type: sony_parsed.album_info.release_type,
            genre: sony_parsed.album_info.genre.unwrap_or_else(|| "Unknown".to_string()),
            label: sony_parsed.album_info.label_name,
            copyright: sony_parsed.album_info.copyright,
            upc: sony_parsed.album_info.icpn,
            grid: Some(sony_parsed.album_info.grid),
            description: None, // Sony 结构中没有 description 字段
        };
        
        // 先转换音频资源，以便在转换曲目时使用
        let audio_resources: Vec<AudioResource> = sony_parsed.audio_resources.iter().map(|sony_audio| {
            // 从文件路径提取文件名
            let file_name = std::path::Path::new(&sony_audio.local_file_path)
                .file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("unknown")
                .to_string();

            // 获取文件大小
            let file_size = std::fs::metadata(&sony_audio.local_file_path)
                .map(|m| m.len())
                .unwrap_or(0);

            AudioResource {
                resource_id: sony_audio.resource_reference.clone(),
                local_path: sony_audio.local_file_path.clone(),
                remote_path: None,
                file_name,
                file_size,
                format: match sony_audio.codec_type.to_lowercase().as_str() {
                    "mp3" => crate::importers::models::AudioFormat::Mp3,
                    "flac" => crate::importers::models::AudioFormat::Flac,
                    "wav" => crate::importers::models::AudioFormat::Wav,
                    "aac" => crate::importers::models::AudioFormat::Aac,
                    "m4a" => crate::importers::models::AudioFormat::M4a,
                    _ => crate::importers::models::AudioFormat::Unknown(sony_audio.codec_type.clone()),
                },
                bitrate: sony_audio.bit_rate,
                sample_rate: sony_audio.sampling_rate.map(|r| {
                    // 将f32转换为u32，乘以10保持一位小数精度
                    // 44.1 -> 441，在数据库中to_string()后变成"441"
                    // 我们需要在数据库插入时特殊处理SONY的采样率
                    (r * 10.0) as u32
                }),
                channels: sony_audio.channels.map(|c| c as u8),
                duration_seconds: None, // Sony 结构中没有音频资源的时长信息
                checksum: Some(sony_audio.md5.clone()),
                uploaded: false,
                uploaded_at: None,
            }
        }).collect();

        // 转换曲目信息
        let tracks: Vec<TrackInfo> = sony_parsed.tracks.into_iter().map(|sony_track| {
            // 从对应的音频资源中提取 bitrate 和 sample_rate
            let (bitrate, sample_rate) = audio_resources
                .iter()
                .find(|audio| audio.resource_id == sony_track.resource_reference)
                .map(|audio| {
                    debug!("🎵 曲目 {} 音频参数: bitrate={:?}, sample_rate={:?}",
                           sony_track.title, audio.bitrate, audio.sample_rate);
                    (audio.bitrate, audio.sample_rate)
                })
                .unwrap_or((None, None));
            let mut provider_metadata = HashMap::new();

            // 保存 Sony 特定的元数据
            provider_metadata.insert("resource_reference".to_string(), sony_track.resource_reference.clone());
            if let Some(ref formal_title) = sony_track.formal_title {
                provider_metadata.insert("formal_title".to_string(), formal_title.clone());
            }

            // 提取艺术家名称（取第一个艺术家）
            let artist_name = sony_track.artists.first()
                .map(|a| a.name.clone())
                .unwrap_or_else(|| "Unknown Artist".to_string());

            // 从贡献者中提取作曲者、作词者、制作人
            let composer = sony_track.contributors.iter()
                .find(|c| c.role.to_lowercase().contains("composer"))
                .map(|c| c.name.clone());
            let lyricist = sony_track.contributors.iter()
                .find(|c| c.role.to_lowercase().contains("lyricist"))
                .map(|c| c.name.clone());
            let producer = sony_track.contributors.iter()
                .find(|c| c.role.to_lowercase().contains("producer"))
                .map(|c| c.name.clone());

            TrackInfo {
                title: sony_track.title,
                artist_name,
                track_number: sony_track.sequence_number as u32,
                disc_number: Some(1), // Sony 结构中没有 disc_number，默认为 1
                duration_seconds: sony_track.duration as u32,
                isrc: Some(sony_track.isrc),
                genre: sony_track.genre,
                composer,
                lyricist,
                producer,
                copyright: Some(album_info.copyright.clone()), // 使用专辑的版权信息
                audio_resource_id: Some(sony_track.resource_reference),
                bitrate,
                sample_rate,
                provider_metadata,
            }
        }).collect();
        
        // 转换图片资源
        let image_resources: Vec<ImageResource> = sony_parsed.image_resources.into_iter().map(|sony_image| {
            // 从文件路径提取文件名
            let file_name = std::path::Path::new(&sony_image.local_file_path)
                .file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("unknown")
                .to_string();

            // 获取文件大小
            let file_size = std::fs::metadata(&sony_image.local_file_path)
                .map(|m| m.len())
                .unwrap_or(0);

            ImageResource {
                resource_id: sony_image.resource_reference,
                local_path: sony_image.local_file_path,
                remote_path: None,
                file_name,
                file_size,
                image_type: match sony_image.image_type.as_str() {
                    "FrontCoverImage" => crate::importers::models::ImageType::AlbumCover,
                    "ArtistImage" => crate::importers::models::ImageType::ArtistPhoto,
                    _ => crate::importers::models::ImageType::Other,
                },
                format: match sony_image.codec_type.to_lowercase().as_str() {
                    "jpg" | "jpeg" => crate::importers::models::ImageFormat::Jpeg,
                    "png" => crate::importers::models::ImageFormat::Png,
                    "gif" => crate::importers::models::ImageFormat::Gif,
                    "bmp" => crate::importers::models::ImageFormat::Bmp,
                    _ => crate::importers::models::ImageFormat::Unknown(sony_image.codec_type),
                },
                width: sony_image.width,
                height: sony_image.height,
                checksum: Some(sony_image.md5),
                uploaded: false,
                uploaded_at: None,
            }
        }).collect();
        
        // 创建提供商信息
        let provider_info = ProviderInfo {
            provider: "sony".to_string(),
            source_file: xml_path.to_string(),
            parsed_at: Utc::now(),
            metadata: HashMap::new(),
        };
        
        Ok(ParsedRelease {
            message_header,
            album_info,
            tracks,
            audio_resources,
            image_resources,
            provider_info,
        })
    }
}
