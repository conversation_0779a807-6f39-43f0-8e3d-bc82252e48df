//! Warner 解析器实现
//!
//! 实现 Parser trait 为 Warner 提供统一的解析接口

use anyhow::Result;
use async_trait::async_trait;
use log::{info, debug};

use crate::importers::models::ParsedRelease;
use crate::importers::warner::{WarnerDdexParser, utils::convert_warner_to_standard, utils::validate_warner_parsed_release};

use super::{Parser, SupportedExtensions};

/// Warner 解析器
pub struct WarnerParser {
    ddex_parser: WarnerDdexParser,
}

impl WarnerParser {
    /// 创建新的 Warner 解析器
    pub fn new() -> Self {
        Self {
            ddex_parser: WarnerDdexParser::new(),
        }
    }
}

#[async_trait]
impl Parser for WarnerParser {
    fn get_provider_name(&self) -> &str {
        "Warner"
    }

    fn validate_xml(&self, xml_path: &str) -> Result<()> {
        debug!("验证 Warner XML 文件: {}", xml_path);

        // 检查文件是否存在
        if !std::path::Path::new(xml_path).exists() {
            return Err(anyhow::anyhow!("XML 文件不存在: {}", xml_path));
        }

        // 读取文件内容进行基本验证
        let content = std::fs::read_to_string(xml_path)?;

        // 检查是否为有效的 XML
        if !content.trim_start().starts_with("<?xml") {
            return Err(anyhow::anyhow!("不是有效的 XML 文件: {}", xml_path));
        }

        // 检查是否包含 DDEX ERN 标识
        if !content.contains("NewReleaseMessage") {
            return Err(anyhow::anyhow!("不是有效的 DDEX ERN 文件: {}", xml_path));
        }

        // Warner 特有的验证：检查是否包含 DealList 或 ReleaseDeal
        if !content.contains("DealList") && !content.contains("ReleaseDeal") {
            debug!("警告: XML 文件可能不是 Warner 格式，缺少 DealList 或 ReleaseDeal 节点");
        }

        debug!("Warner XML 文件验证通过: {}", xml_path);
        Ok(())
    }
    
    async fn parse_xml(&self, xml_path: &str, base_dir: &str) -> Result<ParsedRelease> {
        info!("🎵 开始解析 Warner XML: {}", xml_path);

        // 使用 Warner 专用解析器解析
        let warner_parsed = self.ddex_parser.parse_xml(xml_path, base_dir)
            .map_err(|e| anyhow::anyhow!("Warner XML 解析失败: {}", e))?;

        // 验证解析结果
        validate_warner_parsed_release(&warner_parsed)
            .map_err(|e| anyhow::anyhow!("Warner 解析结果验证失败: {}", e))?;

        // 转换为标准格式
        let standard_parsed = convert_warner_to_standard(warner_parsed, xml_path)
            .map_err(|e| anyhow::anyhow!("Warner 格式转换失败: {}", e))?;

        info!("✅ Warner XML 解析完成: {} (专辑: {}, 曲目: {})",
              xml_path,
              standard_parsed.album_info.title,
              standard_parsed.tracks.len());

        Ok(standard_parsed)
    }
    
    fn get_supported_extensions(&self) -> SupportedExtensions {
        SupportedExtensions {
            audio: vec![
                "mp3".to_string(),
                "flac".to_string(),
                "wav".to_string(),
                "aac".to_string(),
                "m4a".to_string(),
                "ogg".to_string(), // Warner 可能使用 OGG
            ],
            image: vec![
                "jpg".to_string(),
                "jpeg".to_string(),
                "png".to_string(),
                "gif".to_string(),
                "bmp".to_string(),
                "webp".to_string(), // Warner 可能使用 WebP
            ],
        }
    }
    
    fn generate_remote_path(&self, local_path: &str, date: &str) -> String {
        let file_name = std::path::Path::new(local_path)
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown");
        
        format!("Warner/{}/{}", date, file_name)
    }
    
    async fn preprocess_file(&self, xml_path: &str) -> Result<()> {
        debug!("Preprocessing Warner file: {}", xml_path);
        
        // Warner 特定的预处理逻辑
        // 例如：字符编码转换、格式标准化等
        
        Ok(())
    }
    
    async fn postprocess_result(&self, result: &mut ParsedRelease) -> Result<()> {
        debug!("Warner 结果后处理");

        // Warner 特有的后处理逻辑
        // 例如：调整艺术家名称格式、处理特殊字段等

        // 确保专辑艺术家信息完整
        if result.album_info.artist_name.is_empty() && !result.tracks.is_empty() {
            result.album_info.artist_name = result.tracks[0].artist_name.clone();
            debug!("从首曲目补充专辑艺术家信息: {}", result.tracks[0].artist_name);
        }

        // 处理 Warner 特有的流派映射
        for track in &mut result.tracks {
            if let Some(ref genre) = track.genre {
                track.genre = Some(Self::normalize_warner_genre(genre));
            }
        }

        // 添加 Warner 特有的元数据标记
        result.provider_info.metadata.insert(
            "warner_processed".to_string(),
            "true".to_string()
        );

        // 处理交易信息（如果有的话）
        if let Some(deal_count) = result.provider_info.metadata.get("deal_count") {
            if deal_count != "0" {
                debug!("处理了 {} 个交易信息", deal_count);
                result.provider_info.metadata.insert(
                    "has_deal_info".to_string(),
                    "true".to_string()
                );
            }
        }

        Ok(())
    }
}

impl WarnerParser {
    /// 标准化 Warner 流派名称
    fn normalize_warner_genre(genre: &str) -> String {
        match genre.to_lowercase().as_str() {
            "alternative rock" => "Alternative".to_string(),
            "pop rock" => "Pop Rock".to_string(),
            "hard rock" => "Hard Rock".to_string(),
            "heavy metal" => "Metal".to_string(),
            "rhythm & blues" => "R&B".to_string(),
            "hip hop" => "Hip Hop".to_string(),
            "electronic music" => "Electronic".to_string(),
            "country music" => "Country".to_string(),
            "jazz music" => "Jazz".to_string(),
            "classical music" => "Classical".to_string(),
            "world music" => "World".to_string(),
            _ => genre.to_string(),
        }
    }
}

impl Default for WarnerParser {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    use std::fs;

    #[test]
    fn test_warner_parser_creation() {
        let parser = WarnerParser::new();
        assert_eq!(parser.get_provider_name(), "Warner");
    }

    #[test]
    fn test_supported_extensions() {
        let parser = WarnerParser::new();
        let extensions = parser.get_supported_extensions();

        assert!(extensions.audio.contains(&"mp3".to_string()));
        assert!(extensions.audio.contains(&"ogg".to_string()));
        assert!(extensions.image.contains(&"jpg".to_string()));
        assert!(extensions.image.contains(&"webp".to_string()));
    }

    #[test]
    fn test_generate_remote_path() {
        let parser = WarnerParser::new();
        let remote_path = parser.generate_remote_path("/local/path/test.mp3", "20220507");
        assert_eq!(remote_path, "Warner/20220507/test.mp3");
    }

    #[test]
    fn test_validate_xml_file_not_exists() {
        let parser = WarnerParser::new();
        let result = parser.validate_xml("/nonexistent/file.xml");
        assert!(result.is_err());
    }

    #[test]
    fn test_validate_xml_invalid_format() {
        let temp_dir = tempdir().unwrap();
        let xml_path = temp_dir.path().join("invalid.xml");
        fs::write(&xml_path, "not xml content").unwrap();

        let parser = WarnerParser::new();
        let result = parser.validate_xml(xml_path.to_str().unwrap());
        assert!(result.is_err());
    }

    #[test]
    fn test_validate_xml_not_ddex() {
        let temp_dir = tempdir().unwrap();
        let xml_path = temp_dir.path().join("not_ddex.xml");
        fs::write(&xml_path, "<?xml version=\"1.0\"?><root></root>").unwrap();

        let parser = WarnerParser::new();
        let result = parser.validate_xml(xml_path.to_str().unwrap());
        assert!(result.is_err());
    }

    #[test]
    fn test_normalize_warner_genre() {
        assert_eq!(WarnerParser::normalize_warner_genre("alternative rock"), "Alternative");
        assert_eq!(WarnerParser::normalize_warner_genre("pop rock"), "Pop Rock");
        assert_eq!(WarnerParser::normalize_warner_genre("heavy metal"), "Metal");
        assert_eq!(WarnerParser::normalize_warner_genre("unknown"), "unknown");
    }
}
