//! UMG 解析器实现
//!
//! 实现 Parser trait 为 UMG 提供统一的解析接口

use anyhow::Result;
use async_trait::async_trait;
use log::{info, debug};

use crate::importers::models::ParsedRelease;
use crate::importers::umg::{UmgDdexParser, utils::convert_umg_to_standard, utils::validate_umg_parsed_release};

use super::{Parser, SupportedExtensions};

/// UMG 解析器
pub struct UmgParser {
    ddex_parser: UmgDdexParser,
}

impl UmgParser {
    /// 创建新的 UMG 解析器
    pub fn new() -> Self {
        Self {
            ddex_parser: UmgDdexParser::new(),
        }
    }
}

#[async_trait]
impl Parser for UmgParser {
    fn get_provider_name(&self) -> &str {
        "UMG"
    }
    
    fn validate_xml(&self, xml_path: &str) -> Result<()> {
        debug!("验证 UMG XML 文件: {}", xml_path);

        // 检查文件是否存在
        if !std::path::Path::new(xml_path).exists() {
            return Err(anyhow::anyhow!("XML 文件不存在: {}", xml_path));
        }

        // 读取文件内容进行基本验证
        let content = std::fs::read_to_string(xml_path)?;

        // 检查是否为有效的 XML
        if !content.trim_start().starts_with("<?xml") {
            return Err(anyhow::anyhow!("不是有效的 XML 文件: {}", xml_path));
        }

        // 检查是否包含 DDEX ERN 标识
        if !content.contains("NewReleaseMessage") {
            return Err(anyhow::anyhow!("不是有效的 DDEX ERN 文件: {}", xml_path));
        }

        // UMG 特有的验证：检查是否包含 TrackRelease 或 PartyList
        if !content.contains("TrackRelease") && !content.contains("PartyList") {
            debug!("警告: XML 文件可能不是 UMG 格式，缺少 TrackRelease 或 PartyList 节点");
        }

        debug!("UMG XML 文件验证通过: {}", xml_path);
        Ok(())
    }
    
    async fn parse_xml(&self, xml_path: &str, base_dir: &str) -> Result<ParsedRelease> {
        info!("🎵 开始解析 UMG XML: {}", xml_path);

        // 使用 UMG 专用解析器解析
        let umg_parsed = self.ddex_parser.parse_xml(xml_path, base_dir)
            .map_err(|e| anyhow::anyhow!("UMG XML 解析失败: {}", e))?;

        // 验证解析结果
        validate_umg_parsed_release(&umg_parsed)
            .map_err(|e| anyhow::anyhow!("UMG 解析结果验证失败: {}", e))?;

        // 转换为标准格式
        let standard_parsed = convert_umg_to_standard(umg_parsed, xml_path)
            .map_err(|e| anyhow::anyhow!("UMG 格式转换失败: {}", e))?;

        info!("✅ UMG XML 解析完成: {} (专辑: {}, 曲目: {})",
              xml_path,
              standard_parsed.album_info.title,
              standard_parsed.tracks.len());

        Ok(standard_parsed)
    }
    
    fn get_supported_extensions(&self) -> SupportedExtensions {
        SupportedExtensions {
            audio: vec![
                "mp3".to_string(),
                "flac".to_string(),
                "wav".to_string(),
                "aac".to_string(),
                "m4a".to_string(),
                "wma".to_string(), // UMG 可能使用 WMA
            ],
            image: vec![
                "jpg".to_string(),
                "jpeg".to_string(),
                "png".to_string(),
                "gif".to_string(),
                "bmp".to_string(),
                "tiff".to_string(), // UMG 可能使用 TIFF
            ],
        }
    }

    fn generate_remote_path(&self, local_path: &str, date: &str) -> String {
        // UMG 的远程路径格式：UMG/YYYYMMDD/filename
        let filename = std::path::Path::new(local_path)
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown");

        format!("UMG/{}/{}", date, filename)
    }

    async fn preprocess_file(&self, xml_path: &str) -> Result<()> {
        debug!("UMG 文件预处理: {}", xml_path);

        // UMG 特有的预处理逻辑
        // 例如：检查文件编码、处理特殊字符等
        let content = std::fs::read_to_string(xml_path)?;

        // 检查文件编码
        if content.contains('\u{FEFF}') {
            debug!("检测到 BOM 标记，UMG XML 文件可能使用 UTF-8 BOM 编码");
        }

        // 检查是否有特殊的 UMG 命名空间
        if content.contains("xmlns:umg") {
            debug!("检测到 UMG 特有的命名空间");
        }

        Ok(())
    }

    async fn postprocess_result(&self, result: &mut ParsedRelease) -> Result<()> {
        debug!("UMG 结果后处理");

        // UMG 特有的后处理逻辑
        // 例如：调整艺术家名称格式、处理特殊字段等

        // 确保专辑艺术家信息完整
        if result.album_info.artist_name.is_empty() && !result.tracks.is_empty() {
            result.album_info.artist_name = result.tracks[0].artist_name.clone();
            debug!("从首曲目补充专辑艺术家信息: {}", result.tracks[0].artist_name);
        }

        // 处理 UMG 特有的流派映射
        for track in &mut result.tracks {
            if let Some(ref genre) = track.genre {
                track.genre = Some(Self::normalize_umg_genre(genre));
            }
        }

        // 添加 UMG 特有的元数据标记
        result.provider_info.metadata.insert(
            "umg_processed".to_string(),
            "true".to_string()
        );

        Ok(())
    }
}

impl UmgParser {
    /// 标准化 UMG 流派名称
    fn normalize_umg_genre(genre: &str) -> String {
        match genre.to_lowercase().as_str() {
            "pop/rock" => "Pop Rock".to_string(),
            "r&b/soul" => "R&B".to_string(),
            "hip-hop/rap" => "Hip Hop".to_string(),
            "electronic/dance" => "Electronic".to_string(),
            "country/folk" => "Country".to_string(),
            "jazz/blues" => "Jazz".to_string(),
            "classical/opera" => "Classical".to_string(),
            "world/international" => "World".to_string(),
            _ => genre.to_string(),
        }
    }
}

impl Default for UmgParser {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    use std::fs;

    #[test]
    fn test_umg_parser_creation() {
        let parser = UmgParser::new();
        assert_eq!(parser.get_provider_name(), "UMG");
    }

    #[test]
    fn test_supported_extensions() {
        let parser = UmgParser::new();
        let extensions = parser.get_supported_extensions();

        assert!(extensions.audio.contains(&"mp3".to_string()));
        assert!(extensions.audio.contains(&"wma".to_string()));
        assert!(extensions.image.contains(&"jpg".to_string()));
        assert!(extensions.image.contains(&"tiff".to_string()));
    }

    #[test]
    fn test_generate_remote_path() {
        let parser = UmgParser::new();
        let remote_path = parser.generate_remote_path("/local/path/test.mp3", "20220507");
        assert_eq!(remote_path, "UMG/20220507/test.mp3");
    }

    #[test]
    fn test_validate_xml_file_not_exists() {
        let parser = UmgParser::new();
        let result = parser.validate_xml("/nonexistent/file.xml");
        assert!(result.is_err());
    }

    #[test]
    fn test_validate_xml_invalid_format() {
        let temp_dir = tempdir().unwrap();
        let xml_path = temp_dir.path().join("invalid.xml");
        fs::write(&xml_path, "not xml content").unwrap();

        let parser = UmgParser::new();
        let result = parser.validate_xml(xml_path.to_str().unwrap());
        assert!(result.is_err());
    }

    #[test]
    fn test_validate_xml_not_ddex() {
        let temp_dir = tempdir().unwrap();
        let xml_path = temp_dir.path().join("not_ddex.xml");
        fs::write(&xml_path, "<?xml version=\"1.0\"?><root></root>").unwrap();

        let parser = UmgParser::new();
        let result = parser.validate_xml(xml_path.to_str().unwrap());
        assert!(result.is_err());
    }

    #[test]
    fn test_normalize_umg_genre() {
        assert_eq!(UmgParser::normalize_umg_genre("pop/rock"), "Pop Rock");
        assert_eq!(UmgParser::normalize_umg_genre("r&b/soul"), "R&B");
        assert_eq!(UmgParser::normalize_umg_genre("hip-hop/rap"), "Hip Hop");
        assert_eq!(UmgParser::normalize_umg_genre("unknown"), "unknown");
    }
}
