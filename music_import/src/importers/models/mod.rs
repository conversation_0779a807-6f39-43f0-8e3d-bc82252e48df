//! 通用数据模型
//! 
//! 定义所有音乐提供商共享的标准化数据结构

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

pub mod release;
pub mod track;
pub mod resource;

pub use release::*;
pub use track::*;
pub use resource::*;

/// 解析结果的统一表示
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParsedRelease {
    /// 消息头信息
    pub message_header: MessageHeader,
    /// 专辑信息
    pub album_info: AlbumInfo,
    /// 曲目列表
    pub tracks: Vec<TrackInfo>,
    /// 音频资源
    pub audio_resources: Vec<AudioResource>,
    /// 图片资源
    pub image_resources: Vec<ImageResource>,
    /// 提供商特定信息
    pub provider_info: ProviderInfo,
}

/// 消息头信息（标准化）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageHeader {
    /// 消息线程 ID
    pub message_thread_id: String,
    /// 消息 ID
    pub message_id: String,
    /// 发送方信息
    pub sender: PartyInfo,
    /// 接收方信息
    pub recipient: PartyInfo,
    /// 创建时间
    pub created_datetime: String,
    /// 控制类型
    pub control_type: String,
}

/// 参与方信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PartyInfo {
    /// 参与方 ID
    pub party_id: String,
    /// 参与方名称
    pub party_name: String,
    /// 参与方类型
    pub party_type: String,
}

/// 专辑信息（标准化）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlbumInfo {
    /// 专辑标题
    pub title: String,
    /// 艺术家名称
    pub artist_name: String,
    /// 发行日期
    pub release_date: String,
    /// 专辑类型
    pub album_type: String,
    /// 流派
    pub genre: String,
    /// 唱片公司
    pub label: String,
    /// 版权信息
    pub copyright: String,
    /// UPC 码
    pub upc: Option<String>,
    /// GRID 码
    pub grid: Option<String>,
    /// 专辑描述
    pub description: Option<String>,
}

/// 提供商特定信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProviderInfo {
    /// 提供商名称 (sony/warner/umg)
    pub provider: String,
    /// 原始文件路径
    pub source_file: String,
    /// 解析时间
    pub parsed_at: DateTime<Utc>,
    /// 提供商特定的元数据
    pub metadata: std::collections::HashMap<String, String>,
}

/// 解析状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ParseStatus {
    /// 未开始
    NotStarted,
    /// 解析中
    Parsing,
    /// 解析成功
    ParseSuccess,
    /// 解析失败
    ParseFailed,
    /// 上传中
    Uploading,
    /// 上传成功
    UploadSuccess,
    /// 上传失败
    UploadFailed,
    /// 数据库保存中
    DatabaseSaving,
    /// 完全成功
    Complete,
    /// 失败
    Failed,
}

impl ParseStatus {
    /// 是否为最终状态
    pub fn is_final(&self) -> bool {
        matches!(self, ParseStatus::Complete | ParseStatus::Failed)
    }
    
    /// 是否为成功状态
    pub fn is_success(&self) -> bool {
        matches!(self, ParseStatus::Complete)
    }
    
    /// 是否为失败状态
    pub fn is_failed(&self) -> bool {
        matches!(self, ParseStatus::Failed | ParseStatus::ParseFailed | ParseStatus::UploadFailed)
    }
}

/// 解析摘要信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParseSummary {
    /// 文件路径
    pub file_path: String,
    /// 解析状态
    pub status: ParseStatus,
    /// 专辑标题
    pub album_title: String,
    /// 曲目数量
    pub track_count: usize,
    /// 音频文件数量
    pub audio_file_count: usize,
    /// 图片文件数量
    pub image_file_count: usize,
    /// 处理开始时间
    pub started_at: DateTime<Utc>,
    /// 处理结束时间
    pub completed_at: Option<DateTime<Utc>>,
    /// 错误信息
    pub error_message: Option<String>,
}

impl ParseSummary {
    /// 创建新的解析摘要
    pub fn new(file_path: String, album_title: String) -> Self {
        Self {
            file_path,
            status: ParseStatus::NotStarted,
            album_title,
            track_count: 0,
            audio_file_count: 0,
            image_file_count: 0,
            started_at: Utc::now(),
            completed_at: None,
            error_message: None,
        }
    }
    
    /// 标记为完成
    pub fn mark_complete(&mut self) {
        self.status = ParseStatus::Complete;
        self.completed_at = Some(Utc::now());
    }
    
    /// 标记为失败
    pub fn mark_failed(&mut self, error: String) {
        self.status = ParseStatus::Failed;
        self.completed_at = Some(Utc::now());
        self.error_message = Some(error);
    }
}
