use axum::{
    extract::{Path, Query, State},
    http::{HeaderMap, StatusCode, header},
    response::{IntoResponse, Response, AppendHeaders},
    routing::{get, post},
    Router,
    body::StreamBody,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use anyhow::{Result, anyhow};
use log::{info, error};
use crate::crypto::audio_encryption::AudioEncryption;
use tokio_stream::Stream;
use std::pin::Pin;
use std::task::{Context, Poll};
use bytes::Bytes;

pub mod auth;
pub mod token;

/// 音频代理服务
pub struct AudioProxyService {
    encryption: AudioEncryption,
    auth_service: auth::AuthService,
    base_path: String,
}

/// 音频流请求参数
#[derive(Debug, Deserialize)]
pub struct AudioStreamRequest {
    pub file_id: String,
    pub user_id: String,
    pub auth_token: String,
    pub range: Option<String>, // "bytes=0-1048576"
}

/// 音频流响应
#[derive(Debug, Serialize)]
pub struct AudioStreamResponse {
    pub content_type: String,
    pub content_length: Option<u64>,
    pub supports_range: bool,
}

/// Range 请求参数
#[derive(Debug)]
pub struct RangeRequest {
    start: u64,
    end: Option<u64>,
}

impl AudioProxyService {
    pub fn new(encryption: AudioEncryption, base_path: String) -> Self {
        Self {
            encryption,
            auth_service: auth::AuthService::new(),
            base_path,
        }
    }

    /// 创建 HTTP 路由
    pub fn create_router(self) -> Router {
        let state = Arc::new(self);

        Router::new()
            .route("/stream", post(handle_audio_stream_request))
            .route("/audio/:file_id", get(handle_direct_audio_request))
            .route("/health", get(health_check))
            .with_state(state)
    }

    /// 处理音频流请求（主要接口）
    pub async fn serve_audio_stream(&self, request: AudioStreamRequest) -> Result<Response> {
        info!("处理音频流请求: 文件={}, 用户={}", request.file_id, request.user_id);

        // 验证用户权限
        if !self.verify_user_access(&request.user_id, &request.file_id, &request.auth_token).await? {
            return Err(anyhow!("Access denied"));
        }

        // 构建加密文件路径
        let encrypted_path = format!("{}/{}.encrypted", self.base_path, request.file_id);

        if !std::path::Path::new(&encrypted_path).exists() {
            return Err(anyhow!("Audio file not found: {}", request.file_id));
        }

        // 解析 Range 请求
        let range = self.parse_range(&request.range)?;

        // 创建音频流
        self.create_audio_stream(&encrypted_path, range).await
    }

    /// 验证用户访问权限
    async fn verify_user_access(&self, user_id: &str, file_id: &str, auth_token: &str) -> Result<bool> {
        // 这里可以集成数据库查询，验证用户是否有权限访问该文件
        // 简化实现：验证 auth_token
        let token_data = self.auth_service.verify_token(auth_token)?;

        Ok(token_data.user_id == user_id &&
           token_data.file_id == file_id &&
           !token_data.is_expired())
    }

    /// 创建音频流
    async fn create_audio_stream(&self, encrypted_path: &str, range: Option<RangeRequest>) -> Result<Response> {
        match range {
            Some(range_req) => {
                // 处理 Range 请求
                self.create_range_stream(encrypted_path, range_req).await
            }
            None => {
                // 处理完整文件流
                self.create_full_stream(encrypted_path).await
            }
        }
    }

    /// 创建完整文件流
    async fn create_full_stream(&self, encrypted_path: &str) -> Result<Response> {
        info!("创建完整音频流: {}", encrypted_path);

        // 获取文件信息
        let file_info = self.encryption.get_file_info(encrypted_path)?;

        // 创建流式解密器
        let stream = AudioDecryptStream::new(
            encrypted_path.to_string(),
            self.encryption.clone(),
            0,
            file_info.original_size,
        )?;

        let content_length = file_info.original_size.to_string();
        let headers = AppendHeaders([
            (header::CONTENT_TYPE, "audio/mpeg"),
            (header::CONTENT_LENGTH, content_length.as_str()),
            (header::ACCEPT_RANGES, "bytes"),
            (header::CACHE_CONTROL, "no-cache"),
        ]);

        Ok((headers, StreamBody::new(stream)).into_response())
    }

    /// 创建范围流
    async fn create_range_stream(&self, encrypted_path: &str, range: RangeRequest) -> Result<Response> {
        let start = range.start;
        let length = match range.end {
            Some(end) => end - start + 1,
            None => {
                // 如果没有指定结束位置，读取到文件末尾
                let file_info = self.encryption.get_file_info(encrypted_path)?;
                file_info.original_size - start
            }
        };

        info!("创建范围音频流: {} bytes from offset {}", length, start);

        // 创建流式解密器
        let stream = AudioDecryptStream::new(
            encrypted_path.to_string(),
            self.encryption.clone(),
            start,
            length,
        )?;

        let content_length = length.to_string();
        let content_range = format!("bytes {}-{}/{}", start, start + length - 1, "*");
        let headers = AppendHeaders([
            (header::CONTENT_TYPE, "audio/mpeg"),
            (header::CONTENT_LENGTH, content_length.as_str()),
            (header::CONTENT_RANGE, content_range.as_str()),
            (header::ACCEPT_RANGES, "bytes"),
        ]);

        Ok((StatusCode::PARTIAL_CONTENT, headers, StreamBody::new(stream)).into_response())
    }

    /// 解析 Range 字符串
    fn parse_range(&self, range_str: &Option<String>) -> Result<Option<RangeRequest>> {
        match range_str {
            Some(range) => {
                if !range.starts_with("bytes=") {
                    return Ok(None);
                }

                let range_part = &range[6..];
                let parts: Vec<&str> = range_part.split('-').collect();

                if parts.len() != 2 {
                    return Ok(None);
                }

                let start = parts[0].parse::<u64>()?;
                let end = if parts[1].is_empty() {
                    None
                } else {
                    Some(parts[1].parse::<u64>()?)
                };

                Ok(Some(RangeRequest { start, end }))
            }
            None => Ok(None),
        }
    }
}

/// 音频解密流
pub struct AudioDecryptStream {
    encrypted_path: String,
    encryption: AudioEncryption,
    current_offset: u64,
    remaining_bytes: u64,
    chunk_size: usize,
}

impl AudioDecryptStream {
    pub fn new(
        encrypted_path: String,
        encryption: AudioEncryption,
        start_offset: u64,
        total_bytes: u64,
    ) -> Result<Self> {
        Ok(Self {
            encrypted_path,
            encryption,
            current_offset: start_offset,
            remaining_bytes: total_bytes,
            chunk_size: 64 * 1024, // 64KB 块
        })
    }
}

impl Stream for AudioDecryptStream {
    type Item = Result<Bytes, std::io::Error>;

    fn poll_next(mut self: Pin<&mut Self>, _cx: &mut Context<'_>) -> Poll<Option<Self::Item>> {
        if self.remaining_bytes == 0 {
            return Poll::Ready(None);
        }

        let chunk_size = std::cmp::min(self.chunk_size as u64, self.remaining_bytes) as usize;

        match self.encryption.decrypt_range(
            &self.encrypted_path,
            self.current_offset,
            chunk_size as u64,
        ) {
            Ok(data) => {
                self.current_offset += data.len() as u64;
                self.remaining_bytes -= data.len() as u64;
                Poll::Ready(Some(Ok(Bytes::from(data))))
            }
            Err(e) => {
                error!("解密流错误: {}", e);
                Poll::Ready(Some(Err(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    e.to_string(),
                ))))
            }
        }
    }
}

/// HTTP 处理函数 - 音频流请求
async fn handle_audio_stream_request(
    State(service): State<Arc<AudioProxyService>>,
    axum::Json(request): axum::Json<AudioStreamRequest>,
) -> Result<Response, (StatusCode, String)> {
    match service.serve_audio_stream(request).await {
        Ok(response) => Ok(response),
        Err(e) => {
            error!("音频流服务错误: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string()))
        }
    }
}

/// 简化的音频请求参数（兼容性）
#[derive(Debug, Deserialize)]
pub struct SimpleAudioRequest {
    token: String,
}

/// HTTP 处理函数 - 直接音频请求（兼容性）
async fn handle_direct_audio_request(
    State(service): State<Arc<AudioProxyService>>,
    Path(file_id): Path<String>,
    Query(params): Query<SimpleAudioRequest>,
    headers: HeaderMap,
) -> Result<Response, (StatusCode, String)> {
    // 转换为 AudioStreamRequest
    let request = AudioStreamRequest {
        file_id: file_id.clone(),
        user_id: "legacy".to_string(), // 兼容性用户ID
        auth_token: params.token,
        range: headers.get(header::RANGE).and_then(|h| h.to_str().ok()).map(|s| s.to_string()),
    };

    match service.serve_audio_stream(request).await {
        Ok(response) => Ok(response),
        Err(e) => {
            error!("音频服务错误: {}", e);
            Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string()))
        }
    }
}

/// 健康检查
async fn health_check() -> impl IntoResponse {
    "Audio Proxy Service is healthy"
}

/// 解析 Range 头
fn parse_range_header(headers: &HeaderMap) -> Option<RangeRequest> {
    let range_header = headers.get(header::RANGE)?;
    let range_str = range_header.to_str().ok()?;
    
    if !range_str.starts_with("bytes=") {
        return None;
    }

    let range_part = &range_str[6..]; // 移除 "bytes="
    let parts: Vec<&str> = range_part.split('-').collect();
    
    if parts.len() != 2 {
        return None;
    }

    let start = parts[0].parse::<u64>().ok()?;
    let end = if parts[1].is_empty() {
        None
    } else {
        parts[1].parse::<u64>().ok()
    };

    Some(RangeRequest { start, end })
}

/// PHP SDK 生成器
pub struct PhpSdkGenerator;

impl PhpSdkGenerator {
    /// 生成 PHP SDK 代码
    pub fn generate_sdk() -> String {
        r#"
<?php

class AudioProxyClient {
    private $baseUrl;
    private $apiKey;
    
    public function __construct($baseUrl, $apiKey) {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->apiKey = $apiKey;
    }
    
    /**
     * 生成音频访问URL
     */
    public function generateAudioUrl($fileId, $userId, $expiresIn = 3600) {
        $token = $this->generateToken($fileId, $userId, $expiresIn);
        return $this->baseUrl . '/audio/' . $fileId . '?token=' . $token;
    }
    
    /**
     * 生成访问令牌
     */
    private function generateToken($fileId, $userId, $expiresIn) {
        $payload = [
            'file_id' => $fileId,
            'user_id' => $userId,
            'expires_at' => time() + $expiresIn,
            'iat' => time()
        ];
        
        // 这里应该使用 JWT 或类似的安全令牌
        // 简化示例，生产环境需要使用适当的签名
        return base64_encode(json_encode($payload));
    }
    
    /**
     * 获取音频流
     */
    public function getAudioStream($fileId, $userId, $range = null) {
        $url = $this->generateAudioUrl($fileId, $userId);
        
        $headers = [];
        if ($range) {
            $headers[] = 'Range: bytes=' . $range;
        }
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => implode("\r\n", $headers)
            ]
        ]);
        
        return fopen($url, 'r', false, $context);
    }
    
    /**
     * 检查服务健康状态
     */
    public function healthCheck() {
        $url = $this->baseUrl . '/health';
        return file_get_contents($url);
    }
}

// 使用示例
/*
$client = new AudioProxyClient('http://localhost:8080', 'your-api-key');

// 生成音频URL
$audioUrl = $client->generateAudioUrl('song123', 'user456', 3600);

// 获取音频流
$stream = $client->getAudioStream('song123', 'user456', '0-1048576');
*/
"#.to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_range_header_parsing() {
        let mut headers = HeaderMap::new();
        headers.insert(header::RANGE, "bytes=0-1023".parse().unwrap());
        
        let range = parse_range_header(&headers).unwrap();
        assert_eq!(range.start, 0);
        assert_eq!(range.end, Some(1023));
    }

    #[test]
    fn test_php_sdk_generation() {
        let sdk = PhpSdkGenerator::generate_sdk();
        assert!(sdk.contains("class AudioProxyClient"));
        assert!(sdk.contains("generateAudioUrl"));
    }
}
