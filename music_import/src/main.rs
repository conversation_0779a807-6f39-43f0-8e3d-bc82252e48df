use clap::{Arg, Command};
use anyhow::Result;

pub mod config;
pub mod importers;
pub mod models;
pub mod utils;
pub mod server;
pub mod client;
pub mod crypto;

// 引入生成的 gRPC 代码
pub mod file_transfer {
    tonic::include_proto!("file_transfer");
}

#[tokio::main]
async fn main() -> Result<()> {
    // 配置日志级别为 INFO，显示详细处理信息
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();
    
    let matches = Command::new("music_import")
        .version("0.1.0")
        .about("Music Import Tool - Unified client/server for Sony, Warner, and UMG")
        .arg(Arg::new("mode")
            .long("mode")
            .value_parser(["client", "server"])
            .required(true)
            .help("运行模式：client 或 server"))
        .arg(Arg::new("config")
            .short('c')
            .long("config")
            .value_name("FILE")
            .required(true)
            .help("配置文件路径 (JSON 格式)"))
        .get_matches();

    match matches.get_one::<String>("mode").unwrap().as_str() {
        "server" => {
            let config_path = matches.get_one::<String>("config").unwrap();
            server::run_nas_server(config_path).await
        }
        "client" => {
            let config_path = matches.get_one::<String>("config").unwrap();
            client::run_import_client(config_path).await
        }
        _ => unreachable!(),
    }
}
