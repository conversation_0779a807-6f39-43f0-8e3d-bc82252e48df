pub mod audio_encryption;
pub mod legacy_encryption;
pub mod encryption_factory;

#[cfg(test)]
mod integration_tests;

use anyhow::Result;
use async_trait::async_trait;

pub use audio_encryption::AudioEncryption;
pub use legacy_encryption::LegacyEncryption;
pub use encryption_factory::EncryptionFactory;

/// 统一的加密器接口
///
/// 提供统一的加密/解密接口，支持不同的加密实现
#[async_trait]
pub trait Encryptor: Send + Sync {
    /// 加密文件
    ///
    /// # 参数
    /// * `input_path` - 输入文件路径
    /// * `output_path` - 输出文件路径
    /// * `file_id` - 文件标识符
    async fn encrypt_file(&self, input_path: &str, output_path: &str, file_id: &str) -> Result<()>;

    /// 解密文件
    ///
    /// # 参数
    /// * `encrypted_path` - 加密文件路径
    /// * `output_path` - 输出文件路径
    async fn decrypt_file(&self, encrypted_path: &str, output_path: &str) -> Result<()>;

    /// 解密文件的指定范围
    ///
    /// # 参数
    /// * `encrypted_path` - 加密文件路径
    /// * `start` - 起始位置
    /// * `length` - 读取长度
    async fn decrypt_range(&self, encrypted_path: &str, start: u64, length: u64) -> Result<Vec<u8>>;

    /// 获取加密文件信息（可选实现）
    async fn get_file_info(&self, encrypted_path: &str) -> Result<Option<FileInfo>>;
}

/// 文件信息结构
#[derive(Debug, Clone)]
pub struct FileInfo {
    /// 文件ID
    pub file_id: String,
    /// 原始文件大小
    pub original_size: u64,
    /// 加密版本
    pub version: String,
}
