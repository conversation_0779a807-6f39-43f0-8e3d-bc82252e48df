//! 路径生成器模块
//! 
//! 支持老版和新版两种文件路径格式的生成

use anyhow::{Result, anyhow};
use log::debug;
use std::path::{Path, PathBuf};

/// 加密版本枚举（用于确定文件扩展名）
#[derive(Debug, Clone, PartialEq)]
pub enum EncryptionVersion {
    Legacy,  // .bin 扩展名
    V1,      // .encrypted 扩展名
}

impl From<&str> for EncryptionVersion {
    fn from(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "legacy" => EncryptionVersion::Legacy,
            "v1" => EncryptionVersion::V1,
            _ => EncryptionVersion::V1, // 默认使用新版
        }
    }
}

/// 路径生成器
///
/// 负责生成统一的文件路径格式，所有版本都使用相同的目录结构
/// 格式: PROVIDER/YYYYMMDD/AlbumID_Timestamp/FileID.extension
pub struct PathGenerator {
    /// 加密版本（决定文件扩展名）
    encryption_version: EncryptionVersion,
    /// 基础目录
    base_dir: String,
}

impl PathGenerator {
    /// 创建新的路径生成器
    ///
    /// # 参数
    /// * `encryption_version` - 加密版本（决定文件扩展名）
    /// * `base_dir` - 基础目录
    pub fn new(encryption_version: EncryptionVersion, base_dir: String) -> Self {
        Self { encryption_version, base_dir }
    }
    
    /// 生成音频文件路径
    ///
    /// 统一格式: PROVIDER/YYYYMMDD/AlbumID_Timestamp/FileID.extension
    ///
    /// # 参数
    /// * `provider` - 提供商名称 (sony, warner, umg)
    /// * `date` - 日期字符串 (YYYYMMDD)
    /// * `file_id` - 文件标识符
    ///
    /// # 返回
    /// 生成的文件路径
    pub fn generate_audio_path(&self, provider: &str, date: &str, file_id: &str) -> Result<String> {
        // 验证输入参数
        if provider.is_empty() || date.is_empty() || file_id.is_empty() {
            return Err(anyhow!("生成音频路径时参数不能为空"));
        }

        // 验证日期格式 (YYYYMMDD)
        if date.len() != 8 || !date.chars().all(|c| c.is_ascii_digit()) {
            return Err(anyhow!("日期格式错误，应为 YYYYMMDD 格式: {}", date));
        }

        // 解析文件ID，提取专辑ID和曲目信息
        let (album_dir, filename) = self.parse_file_id(file_id)?;

        // 根据加密版本确定文件扩展名
        let extension = match self.encryption_version {
            EncryptionVersion::Legacy => "bin",
            EncryptionVersion::V1 => "encrypted",
        };

        let path = PathBuf::from(&self.base_dir)
            .join(provider.to_uppercase())
            .join(date)
            .join(album_dir)
            .join(format!("{}.{}", filename, extension));

        let path_str = path.to_string_lossy().to_string();
        debug!("生成音频路径: {} (版本: {:?})", path_str, self.encryption_version);

        Ok(path_str)
    }
    
    /// 生成图片文件路径
    ///
    /// 图片文件通常不加密，保持原始扩展名
    /// 格式: PROVIDER/YYYYMMDD/AlbumID_Timestamp/ImageFile.jpg
    ///
    /// # 参数
    /// * `provider` - 提供商名称
    /// * `date` - 日期字符串
    /// * `file_id` - 文件标识符（包含扩展名）
    ///
    /// # 返回
    /// 生成的图片路径
    pub fn generate_image_path(&self, provider: &str, date: &str, file_id: &str) -> Result<String> {
        // 验证输入参数
        if provider.is_empty() || date.is_empty() || file_id.is_empty() {
            return Err(anyhow!("生成图片路径时参数不能为空"));
        }

        // 验证日期格式 (YYYYMMDD)
        if date.len() != 8 || !date.chars().all(|c| c.is_ascii_digit()) {
            return Err(anyhow!("日期格式错误，应为 YYYYMMDD 格式: {}", date));
        }

        // 解析文件ID，提取专辑ID和文件名
        let (album_dir, filename) = self.parse_file_id(file_id)?;

        let path = PathBuf::from(&self.base_dir)
            .join(provider.to_uppercase())
            .join(date)
            .join(album_dir)
            .join(filename); // 图片保持原始扩展名

        let path_str = path.to_string_lossy().to_string();
        debug!("生成图片路径: {}", path_str);

        Ok(path_str)
    }
    
    /// 解析文件ID，提取专辑目录和文件名
    ///
    /// 输入: A10301A0004750949S_70045247_20122
    /// 输出: (A10301A0004750949S_20220503163413610, A10301A0004750949S_70045247_20122)
    fn parse_file_id(&self, file_id: &str) -> Result<(String, String)> {
        // 文件ID格式通常包含专辑ID和曲目信息
        // 这里需要根据实际的文件ID格式进行解析

        // 简化实现：假设文件ID格式为 AlbumID_TrackID_Sequence
        let parts: Vec<&str> = file_id.split('_').collect();

        if parts.len() < 2 {
            return Err(anyhow!("文件ID格式错误: {}", file_id));
        }

        let album_id = parts[0];

        // 生成稳定的时间戳（基于文件ID的哈希值）
        // 这样相同的文件ID总是生成相同的时间戳
        let timestamp = self.generate_stable_timestamp(file_id);
        let album_dir = format!("{}_{}", album_id, timestamp);

        Ok((album_dir, file_id.to_string()))
    }

    /// 生成基于文件ID的稳定时间戳
    ///
    /// 使用文件ID的哈希值生成一个看起来像时间戳的稳定字符串
    /// 这样相同的文件ID总是生成相同的"时间戳"
    fn generate_stable_timestamp(&self, file_id: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        file_id.hash(&mut hasher);
        let hash = hasher.finish();

        // 将哈希值转换为看起来像时间戳的格式
        // 格式：YYYYMMDDHHMMSSXXX
        let year = 2022 + (hash % 3) as u32; // 2022-2024
        let month = 1 + (hash >> 8) % 12; // 1-12
        let day = 1 + (hash >> 16) % 28; // 1-28 (避免月份天数问题)
        let hour = (hash >> 24) % 24; // 0-23
        let minute = (hash >> 32) % 60; // 0-59
        let second = (hash >> 40) % 60; // 0-59
        let millis = (hash >> 48) % 1000; // 0-999

        format!("{:04}{:02}{:02}{:02}{:02}{:02}{:03}",
                year, month, day, hour, minute, second, millis)
    }
    
    /// 确保目录存在
    pub fn ensure_directory_exists(&self, file_path: &str) -> Result<()> {
        let path = Path::new(file_path);
        
        if let Some(parent) = path.parent() {
            if !parent.exists() {
                std::fs::create_dir_all(parent)
                    .map_err(|e| anyhow!("创建目录失败 {}: {}", parent.display(), e))?;
                debug!("创建目录: {}", parent.display());
            }
        }
        
        Ok(())
    }
    
    /// 获取相对路径（用于数据库存储）
    pub fn get_relative_path(&self, full_path: &str) -> String {
        if let Ok(path) = Path::new(full_path).strip_prefix(&self.base_dir) {
            path.to_string_lossy().to_string()
        } else {
            full_path.to_string()
        }
    }
    
    /// 获取完整路径（从相对路径）
    pub fn get_full_path(&self, relative_path: &str) -> String {
        PathBuf::from(&self.base_dir)
            .join(relative_path)
            .to_string_lossy()
            .to_string()
    }
}

/// 从配置字符串创建路径生成器
impl PathGenerator {
    /// 从加密版本字符串创建路径生成器
    pub fn from_encryption_version(version: &str, base_dir: String) -> Self {
        let encryption_version = EncryptionVersion::from(version);
        Self::new(encryption_version, base_dir)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_legacy_audio_path() {
        let generator = PathGenerator::new(
            EncryptionVersion::Legacy,
            "/test".to_string()
        );

        let path = generator.generate_audio_path(
            "sony",
            "20220507",
            "A10301A0004750949S_70045247_20122"
        ).unwrap();

        assert!(path.contains("SONY"));
        assert!(path.contains("20220507"));
        assert!(path.ends_with(".bin"));
    }

    #[test]
    fn test_generate_v1_audio_path() {
        let generator = PathGenerator::new(
            EncryptionVersion::V1,
            "/test".to_string()
        );

        let path = generator.generate_audio_path(
            "sony",
            "20220507",
            "A10301A0004750949S_70045247_20122"
        ).unwrap();

        assert!(path.contains("SONY"));
        assert!(path.contains("20220507"));
        assert!(path.ends_with(".encrypted"));
    }
    
    #[test]
    fn test_ensure_directory_exists() {
        let generator = PathGenerator::new(
            EncryptionVersion::V1,
            "/tmp/test".to_string()
        );

        let test_path = "/tmp/test/subdir/file.txt";

        // 注意：这个测试在实际环境中可能需要权限
        // 这里只测试方法不会崩溃
        let _ = generator.ensure_directory_exists(test_path);
    }

    #[test]
    fn test_relative_and_full_path() {
        let base_dir = "/test/base".to_string();
        let generator = PathGenerator::new(EncryptionVersion::V1, base_dir.clone());

        let full_path = format!("{}/SONY/20220507/album/file.encrypted", base_dir);
        let relative_path = generator.get_relative_path(&full_path);

        assert_eq!(relative_path, "SONY/20220507/album/file.encrypted");

        let reconstructed_full_path = generator.get_full_path(&relative_path);
        assert_eq!(reconstructed_full_path, full_path);
    }

    #[test]
    fn test_invalid_date_format() {
        let generator = PathGenerator::new(
            EncryptionVersion::Legacy,
            "/test".to_string()
        );

        let result = generator.generate_audio_path(
            "sony",
            "invalid_date",
            "A1"
        );

        assert!(result.is_err());
    }

    #[test]
    fn test_encryption_version_from_string() {
        assert_eq!(EncryptionVersion::from("legacy"), EncryptionVersion::Legacy);
        assert_eq!(EncryptionVersion::from("v1"), EncryptionVersion::V1);
        assert_eq!(EncryptionVersion::from("unknown"), EncryptionVersion::V1); // 默认值
    }

    #[test]
    fn test_generate_image_path() {
        let generator = PathGenerator::new(
            EncryptionVersion::Legacy,
            "/test".to_string()
        );

        let path = generator.generate_image_path(
            "sony",
            "20220507",
            "cover.jpg"
        ).unwrap();

        assert!(path.contains("SONY"));
        assert!(path.contains("20220507"));
        assert!(path.ends_with("cover.jpg")); // 图片保持原始扩展名
    }
}
