use tonic::transport::Channel;
use tokio::fs::File;
use tokio::io::{AsyncReadExt, AsyncSeekExt};
use tokio_stream::wrappers::ReceiverStream;
use anyhow::{Result, Context};
use log::{info, error, warn};
use uuid::Uuid;
use crate::utils::file_hash;

pub use crate::file_transfer::file_transfer_service_client::FileTransferServiceClient;
pub use crate::file_transfer::{FileChunk, StatusRequest};

pub struct GrpcFileClient {
    client: FileTransferServiceClient<Channel>,
    chunk_size: usize,
}

impl GrpcFileClient {
    pub async fn new(server_addr: &str) -> Result<Self> {
        info!("🔗 尝试连接到 gRPC 服务器: {}", server_addr);

        // 尝试多种地址格式
        let addresses_to_try = vec![
            server_addr.to_string(),
            format!("http://{}", server_addr),
            format!("https://{}", server_addr),
        ];

        let mut last_error_msg = String::new();

        for addr in addresses_to_try {
            info!("🔍 尝试地址格式: {}", addr);

            match Channel::from_shared(addr.clone()) {
                Ok(endpoint) => {
                    match endpoint
                        .timeout(std::time::Duration::from_secs(30))
                        .connect_timeout(std::time::Duration::from_secs(10))
                        .connect()
                        .await
                    {
                        Ok(channel) => {
                            let client = FileTransferServiceClient::new(channel);
                            info!("✅ 成功连接到 gRPC 服务器: {}", addr);

                            return Ok(Self {
                                client,
                                chunk_size: 1024 * 1024, // 1MB chunks
                            });
                        }
                        Err(e) => {
                            let error_msg = format!("连接失败 ({}): {}", addr, e);
                            warn!("❌ {}", error_msg);
                            last_error_msg = error_msg;
                        }
                    }
                }
                Err(e) => {
                    let error_msg = format!("地址格式无效 ({}): {}", addr, e);
                    warn!("❌ {}", error_msg);
                    last_error_msg = error_msg;
                }
            }
        }

        Err(anyhow::anyhow!(
            "Failed to connect to gRPC server at {} after trying multiple formats. Last error: {}",
            server_addr,
            last_error_msg
        ))
    }
    
    pub async fn upload_file_resumable(&mut self, local_path: &str, remote_path: &str) -> Result<bool> {
        let file_id = Uuid::new_v4().to_string();

        // 计算本地文件校验和和大小
        let local_hash = file_hash::calculate_file_hash(local_path).await
            .context("Failed to calculate file hash")?;

        let mut file = File::open(local_path).await
            .context(format!("Failed to open file: {}", local_path))?;
        let file_size = file.metadata().await?.len();

        // 检查上传状态（包含校验和）
        let status_response = self.client
            .check_upload_status(StatusRequest {
                file_id: remote_path.to_string(),
                file_hash: local_hash.clone(),
                file_size,
            })
            .await?;

        let status = status_response.into_inner();

        // 如果服务器端文件已存在且校验和匹配，跳过上传
        if status.exists && status.is_complete && status.server_hash == local_hash {
            info!("✅ 文件已存在且校验和匹配，跳过上传: {}", remote_path);
            return Ok(false); // 返回 false 表示跳过了上传
        }

        let mut uploaded_bytes = status.bytes_uploaded;
        
        info!("Uploading file: {} -> {} (resume from {} bytes)", local_path, remote_path, uploaded_bytes);
        
        // 跳到断点位置
        file.seek(std::io::SeekFrom::Start(uploaded_bytes)).await?;
        
        let (tx, rx) = tokio::sync::mpsc::channel(100);
        let stream = ReceiverStream::new(rx);
        
        // 启动上传任务
        let file_id_clone = file_id.clone();
        let remote_path_clone = remote_path.to_string();
        let chunk_size = self.chunk_size;
        
        tokio::spawn(async move {
            while uploaded_bytes < file_size {
                let remaining = file_size - uploaded_bytes;
                let current_chunk_size = std::cmp::min(chunk_size, remaining as usize);
                let mut buffer = vec![0u8; current_chunk_size];
                
                match file.read_exact(&mut buffer).await {
                    Ok(_) => {
                        let is_last = uploaded_bytes + current_chunk_size as u64 >= file_size;
                        let chunk = FileChunk {
                            file_id: file_id_clone.clone(),
                            offset: uploaded_bytes,
                            data: buffer,
                            total_size: file_size,
                            is_last_chunk: is_last,
                            file_name: remote_path_clone.clone(),
                        };
                        
                        if tx.send(chunk).await.is_err() {
                            error!("Failed to send chunk");
                            break;
                        }
                        
                        uploaded_bytes += current_chunk_size as u64;
                        
                        if uploaded_bytes % (10 * 1024 * 1024) == 0 || is_last {
                            info!("Upload progress: {}/{} bytes ({:.1}%)", 
                                uploaded_bytes, file_size, 
                                (uploaded_bytes as f64 / file_size as f64) * 100.0);
                        }
                        
                        if is_last {
                            break;
                        }
                    }
                    Err(e) => {
                        error!("Failed to read file: {}", e);
                        break;
                    }
                }
            }
        });
        
        let response = self.client.upload_file(stream).await?;
        let result = response.into_inner();
        
        if result.success {
            info!("Upload completed: {} ({} bytes)", remote_path, result.bytes_received);
            Ok(true) // 返回 true 表示成功上传
        } else {
            error!("Upload failed: {}", result.message);
            Err(anyhow::anyhow!("Upload failed: {}", result.message))
        }
    }
}