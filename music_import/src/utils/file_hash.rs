use anyhow::Result;
use sha2::{Sha256, Digest};
use tokio::fs::File;
use tokio::io::{AsyncReadExt, BufReader};
use std::path::Path;

/// 计算文件的 SHA256 校验和
pub async fn calculate_file_hash<P: AsRef<Path>>(file_path: P) -> Result<String> {
    let file = File::open(file_path).await?;
    let mut reader = BufReader::new(file);
    let mut hasher = Sha256::new();
    let mut buffer = vec![0u8; 8192]; // 8KB 缓冲区

    loop {
        let bytes_read = reader.read(&mut buffer).await?;
        if bytes_read == 0 {
            break;
        }
        hasher.update(&buffer[..bytes_read]);
    }

    let result = hasher.finalize();
    Ok(format!("{:x}", result))
}

/// 同步版本的文件校验和计算（用于服务器端）
pub fn calculate_file_hash_sync<P: AsRef<Path>>(file_path: P) -> Result<String> {
    use std::fs::File;
    use std::io::{BufReader, Read};
    
    let file = File::open(file_path)?;
    let mut reader = BufReader::new(file);
    let mut hasher = Sha256::new();
    let mut buffer = vec![0u8; 8192];

    loop {
        let bytes_read = reader.read(&mut buffer)?;
        if bytes_read == 0 {
            break;
        }
        hasher.update(&buffer[..bytes_read]);
    }

    let result = hasher.finalize();
    Ok(format!("{:x}", result))
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;

    #[tokio::test]
    async fn test_file_hash_calculation() {
        // 创建临时文件
        let temp_dir = std::env::temp_dir();
        let temp_path = temp_dir.join("test_file.txt");
        let test_content = b"Hello, World!";

        tokio::fs::write(&temp_path, test_content).await.unwrap();

        // 计算校验和
        let hash = calculate_file_hash(&temp_path).await.unwrap();

        // 验证校验和（"Hello, World!" 的 SHA256）
        let expected = "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f";
        assert_eq!(hash, expected);

        // 清理
        let _ = tokio::fs::remove_file(&temp_path).await;
    }

    #[test]
    fn test_file_hash_sync() {
        let temp_dir = std::env::temp_dir();
        let temp_path = temp_dir.join("test_file_sync.txt");
        let test_content = b"Hello, World!";

        std::fs::write(&temp_path, test_content).unwrap();

        let hash = calculate_file_hash_sync(&temp_path).unwrap();
        let expected = "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f";
        assert_eq!(hash, expected);

        // 清理
        let _ = std::fs::remove_file(&temp_path);
    }
}
