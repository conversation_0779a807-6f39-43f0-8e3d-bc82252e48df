use anyhow::Result;
use tonic::transport::Server;
use crate::config::Config;
use std::path::PathBuf;

pub mod file_transfer;

pub async fn run_nas_server(config_path: &str) -> Result<()> {
    let config = Config::from_file(&PathBuf::from(config_path), "server")?;
    
    let addr = format!("{}:{}", 
        config.server.as_ref().unwrap().host, 
        config.server.as_ref().unwrap().port
    ).parse()?;
    
    let upload_dir = config.server.as_ref().unwrap().upload_dir.clone();
    let file_service = file_transfer::FileTransferServer::new(upload_dir);
    
    println!("🚀 NAS Server starting on {}", addr);
    
    Server::builder()
        .add_service(file_transfer::FileTransferServiceServer::new(file_service))
        .serve(addr)
        .await?;
        
    Ok(())
}