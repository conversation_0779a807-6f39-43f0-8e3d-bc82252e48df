[package]
name = "music_import"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A unified music import tool for Sony, Warner, and UMG"

[lib]
name = "music_import"
path = "src/lib.rs"

[[bin]]
name = "music_import"
path = "src/main.rs"

[[example]]
name = "key_rotation_demo"
path = "examples/key_rotation_demo.rs"



[dependencies]
# 命令行参数解析
clap = { version = "4.4", features = ["derive"] }

# 并行处理
rayon = "1.8"

# XML处理
roxmltree = "0.18"

# 序列化/反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 数据库
mysql = "24.0"

# 日期时间处理
chrono = { version = "0.4", features = ["serde"] }

# 异步运行时
tokio = { version = "1.32", features = ["full"] }

# 日志
log = "0.4"
env_logger = "0.10"

# 邮件
lettre = { version = "0.10", features = ["builder", "smtp-transport"] }

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 文件系统操作
walkdir = "2.4"

# HTTP客户端
reqwest = { version = "0.11", features = ["json"] }
md5 = "0.7"
humantime = "2.1"
url = "2.4"

# gRPC 相关依赖
tonic = "0.10"
prost = "0.12"
uuid = { version = "1.0", features = ["v4"] }
tokio-stream = "0.1"

# 音频加密相关依赖
aes-gcm = "0.10"
sha2 = "0.10"
hmac = "0.12"
rand = "0.8"
hex = "0.4"
base64 = "0.21"

# HTTP 服务器 (音频代理)
axum = "0.6"
bytes = "1.0"

# 异步 trait 支持
async-trait = "0.1"

# 临时文件
tempfile = "3.8"

# 加密和哈希 (已在上面定义)

[dev-dependencies]
tempfile = "3.8"
tokio-test = "0.4"

[build-dependencies]
tonic-build = "0.10"
