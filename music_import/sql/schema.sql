-- -- 音乐导入系统数据库表结构
-- -- 修复版本 - 解决了表创建顺序、字段命名、索引等问题

-- -- 设置数据库字符集
-- SET NAMES utf8mb4;
-- SET FOREIGN_KEY_CHECKS = 0;

-- -- 1. 专辑信息表
-- CREATE TABLE albums (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '专辑ID',
--     title VARCHAR(255) NOT NULL COMMENT '专辑标题',
--     grid VARCHAR(100) UNIQUE NOT NULL COMMENT 'GRid 全球发行标识符',
--     icpn VARCHAR(100) COMMENT 'ICPN 国际目录产品编号',
--     release_type VARCHAR(100) COMMENT '发行类型',
--     release_date DATE COMMENT '发行日期',
--     duration BIGINT COMMENT '总时长(秒)',
--     copyright TEXT COMMENT '版权信息',
--     cover_path VARCHAR(500) COMMENT '封面图片路径',
--     label_name VA<PERSON><PERSON><PERSON>(255) COMMENT '发行商名称',
--     display_artist_name <PERSON><PERSON><PERSON><PERSON>(255) COMMENT '显示艺术家名称',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
--     -- 索引
--     INDEX idx_grid (grid),
--     INDEX idx_release_date (release_date),
--     INDEX idx_label_name (label_name),
--     INDEX idx_title (title)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='专辑信息表';

-- -- 2. 艺术家主表 (必须在 track_artists 之前创建)
-- CREATE TABLE artists (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '艺术家ID',
--     canonical_name VARCHAR(255) NOT NULL COMMENT '标准名称(通常是英文或原始语言)',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
--     -- 索引
--     INDEX idx_canonical_name (canonical_name)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='艺术家信息表';

-- -- 3. 艺术家多语言名称表
-- CREATE TABLE artist_names (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '名称ID',
--     artist_id BIGINT NOT NULL COMMENT '艺术家ID',
--     name VARCHAR(191) NOT NULL COMMENT '艺术家名称',
--     language_code VARCHAR(10) COMMENT '语言代码(如: en, ja-Kana, zh-Hans)',
--     is_primary BOOLEAN DEFAULT FALSE COMMENT '是否为该语言的主要名称',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
--     -- 外键约束
--     FOREIGN KEY (artist_id) REFERENCES artists(id) ON DELETE CASCADE,
    
--     -- 索引
--     INDEX idx_artist_id (artist_id),
--     INDEX idx_language_code (language_code),
--     INDEX idx_name (name),
--     UNIQUE KEY unique_artist_language (artist_id, language_code, name(100))
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='艺术家多语言名称表';

-- -- 4. 曲目信息表
-- CREATE TABLE tracks (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '曲目ID',
--     album_id BIGINT NOT NULL COMMENT '所属专辑ID',
--     title VARCHAR(255) NOT NULL COMMENT '曲目标题',
--     isrc VARCHAR(50) UNIQUE NOT NULL COMMENT 'ISRC 国际标准录音制品编码',
--     duration BIGINT COMMENT '时长(秒)',
--     file_path VARCHAR(500) COMMENT 'NAS文件路径',
--     md5 VARCHAR(32) COMMENT '文件MD5哈希',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
--     -- 外键约束
--     FOREIGN KEY (album_id) REFERENCES albums(id) ON DELETE CASCADE,
    
--     -- 索引
--     INDEX idx_album_id (album_id),
--     INDEX idx_isrc (isrc),
--     INDEX idx_title (title)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='曲目信息表';

-- -- 5. 曲目艺术家关联表
-- CREATE TABLE track_artists (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
--     track_id BIGINT NOT NULL COMMENT '曲目ID',
--     artist_id BIGINT NOT NULL COMMENT '艺术家ID',
--     role VARCHAR(100) COMMENT '在该曲目中的角色',
--     sequence_number INT COMMENT '序号',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
--     -- 外键约束
--     FOREIGN KEY (track_id) REFERENCES tracks(id) ON DELETE CASCADE,
--     FOREIGN KEY (artist_id) REFERENCES artists(id) ON DELETE CASCADE,
    
--     -- 索引
--     INDEX idx_track_id (track_id),
--     INDEX idx_artist_id (artist_id),
--     UNIQUE KEY unique_track_artist_role (track_id, artist_id, role(50))
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='曲目艺术家关联表';

-- -- 6. 导入处理日志表
-- CREATE TABLE import_logs (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
--     provider VARCHAR(50) NOT NULL COMMENT '提供商(sony/warner/umg)',
--     file_path VARCHAR(500) NOT NULL COMMENT '源文件路径',
--     album_id BIGINT COMMENT '关联的专辑ID',
--     status ENUM('processing', 'success', 'failed') NOT NULL COMMENT '处理状态',
--     error_message TEXT COMMENT '错误信息',
--     start_time TIMESTAMP COMMENT '开始时间',
--     end_time TIMESTAMP COMMENT '结束时间',
--     duration_ms BIGINT COMMENT '处理耗时(毫秒)',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
--     -- 外键约束
--     FOREIGN KEY (album_id) REFERENCES albums(id) ON DELETE SET NULL,
    
--     -- 索引
--     INDEX idx_provider (provider),
--     INDEX idx_status (status),
--     INDEX idx_start_time (start_time),
--     INDEX idx_file_path (file_path(255))
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='导入处理日志表';

-- -- 7. 改进的搜索索引表
-- CREATE TABLE search_index (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '搜索索引ID',
--     entity_type ENUM('album', 'track', 'artist') NOT NULL COMMENT '实体类型',
--     entity_id BIGINT NOT NULL COMMENT '实体ID',
--     title VARCHAR(255) NOT NULL COMMENT '标题/名称',
--     searchable_text TEXT NOT NULL COMMENT '可搜索文本',
--     album_title VARCHAR(255) COMMENT '所属专辑标题',
--     artist_names TEXT COMMENT '艺术家名称(逗号分隔)',
--     relevance_score DECIMAL(5,2) DEFAULT 1.0 COMMENT '相关性权重',
--     is_primary BOOLEAN DEFAULT TRUE COMMENT '是否为主要搜索记录',
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

--     -- 全文索引
--     FULLTEXT KEY ft_title (title),
--     FULLTEXT KEY ft_searchable_text (searchable_text),
--     FULLTEXT KEY ft_combined (title, searchable_text),

--     -- 普通索引
--     INDEX idx_entity_type (entity_type),
--     INDEX idx_entity_id (entity_id),
--     INDEX idx_title (title),
--     INDEX idx_is_primary (is_primary),

--     -- 复合索引
--     INDEX idx_entity_type_id (entity_type, entity_id),
--     INDEX idx_entity_primary (entity_type, entity_id, is_primary),

--     -- 唯一约束 (防止重复记录)
--     UNIQUE KEY unique_entity_primary (entity_type, entity_id, is_primary)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索索引表';

-- -- 8. 数据同步触发器

-- -- 专辑插入触发器
-- DELIMITER $$
-- CREATE DEFINER=`new_avs`@`%` TRIGGER album_search_insert
-- AFTER INSERT ON albums
-- FOR EACH ROW
-- BEGIN
--     INSERT INTO search_index (
--         entity_type, entity_id, title, searchable_text,
--         album_title, artist_names, is_primary
--     ) VALUES (
--         'album',
--         NEW.id,
--         NEW.title,
--         CONCAT(NEW.title, ' ', IFNULL(NEW.display_artist_name, ''), ' ', IFNULL(NEW.label_name, '')),
--         NEW.title,
--         NEW.display_artist_name,
--         TRUE
--     );
-- END$$

-- -- 专辑更新触发器
-- CREATE DEFINER=`new_avs`@`%` TRIGGER album_search_update
-- AFTER UPDATE ON albums
-- FOR EACH ROW
-- BEGIN
--     UPDATE search_index SET
--         title = NEW.title,
--         searchable_text = CONCAT(NEW.title, ' ', IFNULL(NEW.display_artist_name, ''), ' ', IFNULL(NEW.label_name, '')),
--         album_title = NEW.title,
--         artist_names = NEW.display_artist_name,
--         updated_at = NOW()
--     WHERE entity_type = 'album' AND entity_id = NEW.id;
-- END$$

-- -- 专辑删除触发器
-- CREATE DEFINER=`new_avs`@`%` TRIGGER album_search_delete
-- AFTER DELETE ON albums
-- FOR EACH ROW
-- BEGIN
--     DELETE FROM search_index
--     WHERE entity_type = 'album' AND entity_id = OLD.id;
-- END$$

-- -- 曲目插入触发器
-- CREATE DEFINER=`new_avs`@`%` TRIGGER track_search_insert
-- AFTER INSERT ON tracks
-- FOR EACH ROW
-- BEGIN
--     DECLARE album_title_var VARCHAR(255);
--     DECLARE artist_names_var TEXT;

--     -- 获取专辑标题
--     SELECT title INTO album_title_var
--     FROM albums WHERE id = NEW.album_id;

--     -- 获取艺术家名称 (简化版)
--     SELECT GROUP_CONCAT(DISTINCT a.canonical_name ORDER BY ta.sequence_number SEPARATOR ', ')
--     INTO artist_names_var
--     FROM track_artists ta
--     JOIN artists a ON ta.artist_id = a.id
--     WHERE ta.track_id = NEW.id;

--     INSERT INTO search_index (
--         entity_type, entity_id, title, searchable_text,
--         album_title, artist_names, is_primary
--     ) VALUES (
--         'track',
--         NEW.id,
--         NEW.title,
--         CONCAT(NEW.title, ' ', IFNULL(album_title_var, ''), ' ', IFNULL(artist_names_var, '')),
--         album_title_var,
--         artist_names_var,
--         TRUE
--     );
-- END$$

-- -- 曲目更新触发器
-- CREATE DEFINER=`new_avs`@`%` TRIGGER track_search_update
-- AFTER UPDATE ON tracks
-- FOR EACH ROW
-- BEGIN
--     DECLARE album_title_var VARCHAR(255);
--     DECLARE artist_names_var TEXT;

--     SELECT title INTO album_title_var
--     FROM albums WHERE id = NEW.album_id;

--     SELECT GROUP_CONCAT(DISTINCT a.canonical_name ORDER BY ta.sequence_number SEPARATOR ', ')
--     INTO artist_names_var
--     FROM track_artists ta
--     JOIN artists a ON ta.artist_id = a.id
--     WHERE ta.track_id = NEW.id;

--     UPDATE search_index SET
--         title = NEW.title,
--         searchable_text = CONCAT(NEW.title, ' ', IFNULL(album_title_var, ''), ' ', IFNULL(artist_names_var, '')),
--         album_title = album_title_var,
--         artist_names = artist_names_var,
--         updated_at = NOW()
--     WHERE entity_type = 'track' AND entity_id = NEW.id;
-- END$$

-- -- 曲目删除触发器
-- CREATE DEFINER=`new_avs`@`%` TRIGGER track_search_delete
-- AFTER DELETE ON tracks
-- FOR EACH ROW
-- BEGIN
--     DELETE FROM search_index
--     WHERE entity_type = 'track' AND entity_id = OLD.id;
-- END$$

-- -- 艺术家插入触发器
-- CREATE DEFINER=`new_avs`@`%` TRIGGER artist_search_insert
-- AFTER INSERT ON artists
-- FOR EACH ROW
-- BEGIN
--     INSERT INTO search_index (
--         entity_type, entity_id, title, searchable_text, is_primary
--     ) VALUES (
--         'artist',
--         NEW.id,
--         NEW.canonical_name,
--         NEW.canonical_name,
--         TRUE
--     );
-- END$$

-- -- 艺术家更新触发器
-- CREATE DEFINER=`new_avs`@`%` TRIGGER artist_search_update
-- AFTER UPDATE ON artists
-- FOR EACH ROW
-- BEGIN
--     UPDATE search_index SET
--         title = NEW.canonical_name,
--         searchable_text = NEW.canonical_name,
--         updated_at = NOW()
--     WHERE entity_type = 'artist' AND entity_id = NEW.id AND is_primary = TRUE;
-- END$$

-- -- 艺术家删除触发器
-- CREATE DEFINER=`new_avs`@`%` TRIGGER artist_search_delete
-- AFTER DELETE ON artists
-- FOR EACH ROW
-- BEGIN
--     DELETE FROM search_index
--     WHERE entity_type = 'artist' AND entity_id = OLD.id;
-- END$$



-- DELIMITER ;

-- -- 9. 搜索优化视图
-- CREATE VIEW search_results_view AS
-- SELECT
--     entity_type,
--     entity_id,
--     title,
--     searchable_text,
--     album_title,
--     artist_names,
--     relevance_score,
--     is_primary
-- FROM search_index;

-- -- 10. 数据完整性检查存储过程
-- DELIMITER $$
-- CREATE PROCEDURE check_search_index_integrity()
-- BEGIN
--     -- 检查孤儿记录
--     SELECT 'Orphaned album records' as issue_type, COUNT(*) as count
--     FROM search_index si
--     LEFT JOIN albums a ON si.entity_id = a.id
--     WHERE si.entity_type = 'album' AND a.id IS NULL

--     UNION ALL

--     SELECT 'Orphaned track records' as issue_type, COUNT(*) as count
--     FROM search_index si
--     LEFT JOIN tracks t ON si.entity_id = t.id
--     WHERE si.entity_type = 'track' AND t.id IS NULL

--     UNION ALL

--     SELECT 'Orphaned artist records' as issue_type, COUNT(*) as count
--     FROM search_index si
--     LEFT JOIN artists ar ON si.entity_id = ar.id
--     WHERE si.entity_type = 'artist' AND ar.id IS NULL;
-- END$$

-- -- 11. 搜索索引重建存储过程
-- CREATE PROCEDURE rebuild_search_index()
-- BEGIN
--     -- 清空搜索索引
--     DELETE FROM search_index;

--     -- 重建专辑索引
--     INSERT INTO search_index (entity_type, entity_id, title, searchable_text, album_title, artist_names, is_primary)
--     SELECT
--         'album',
--         id,
--         title,
--         CONCAT(title, ' ', IFNULL(display_artist_name, ''), ' ', IFNULL(label_name, '')),
--         title,
--         display_artist_name,
--         TRUE
--     FROM albums;

--     -- 重建曲目索引
--     INSERT INTO search_index (entity_type, entity_id, title, searchable_text, album_title, artist_names, is_primary)
--     SELECT
--         'track',
--         t.id,
--         t.title,
--         CONCAT(t.title, ' ', a.title, ' ', IFNULL(a.display_artist_name, '')),
--         a.title,
--         a.display_artist_name,
--         TRUE
--     FROM tracks t
--     JOIN albums a ON t.album_id = a.id;

--     -- 重建艺术家索引
--     INSERT INTO search_index (entity_type, entity_id, title, searchable_text, is_primary)
--     SELECT
--         'artist',
--         id,
--         canonical_name,
--         canonical_name,
--         TRUE
--     FROM artists;

--     -- 注意：artist_names 表仅用于数据记录，不自动关联搜索索引
--     -- 如需多语言搜索，可手动插入 search_index 记录

-- END$$
-- DELIMITER ;

-- -- 恢复外键检查
-- SET FOREIGN_KEY_CHECKS = 1;
