CREATE TABLE `avs`.`album`  (
  `album_id` int(15) NOT NULL AUTO_INCREMENT COMMENT 'Unique Album ID',
  `album_name` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'Album Name',
  `grid` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `icpn` varchar(14) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'Identification of the Release. Only applicable when the Release is an abstraction of a complete physical Product. ',
  `releasedate` date NULL DEFAULT NULL,
  `startdate` date NULL DEFAULT NULL COMMENT 'the start date this album can be able to use',
  `enddate` date NULL DEFAULT NULL COMMENT 'the end date this album can be able to use',
  `duration` time NULL DEFAULT NULL,
  `cover_link` varchar(5120) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Album Cover Link',
  `usagecount` int(11) NULL DEFAULT 0 COMMENT 'Usage count',
  `active` int(1) NULL DEFAULT 1,
  `created_date` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `isCustom` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'to indicate if this album can only be used for some users\n0: all can use , 1: only selected users can use',
  `domain` smallint(2) NOT NULL DEFAULT 1 COMMENT '# we dont bind to license becasue license can change\nindicatethe domain this track locates, default is 1',
  PRIMARY KEY (`album_id`) USING BTREE,
  UNIQUE INDEX `album_id_UNIQUE`(`album_id`) USING BTREE,
  INDEX `albumsort_index`(`releasedate`, `album_id`) USING BTREE,
  INDEX `albumTakeDown`(`active`, `grid`, `icpn`) USING BTREE,
  INDEX `albumIdActive`(`album_id`, `active`) USING BTREE,
  INDEX `idx_usage_album`(`usagecount`, `album_id`) USING BTREE,
  INDEX `idx_search_release_active`(`releasedate`, `active`, `album_id`) USING BTREE,
  INDEX `idx_search_album_status`(`album_id`, `active`, `releasedate`) USING BTREE,
  INDEX `idx_album_active_release`(`active`, `releasedate`, `album_id`) USING BTREE,
  INDEX `idx_album_domain_release`(`domain`, `releasedate`, `album_id`) USING BTREE,
  FULLTEXT INDEX `albumName`(`album_name`)
) ENGINE = InnoDB AUTO_INCREMENT = 554872 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'Album database' ROW_FORMAT = Compact;

CREATE TABLE `avs`.`track`  (
  `track_id` int(15) NOT NULL AUTO_INCREMENT,
  `duration` time(6) NULL DEFAULT NULL,
  `isrc` varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `discno` int(2) NOT NULL DEFAULT 1,
  `trackno` int(2) NOT NULL DEFAULT 1,
  `trackname` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `releasetype` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'Track Release',
  `year` smallint(4) NULL DEFAULT NULL,
  `month` smallint(2) NULL DEFAULT NULL,
  `bitrate` int(11) NULL DEFAULT NULL,
  `samplingrate` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `filelocation` varchar(5120) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'File Location',
  `rating` float NULL DEFAULT NULL,
  `copyright` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_date` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `active` int(1) NOT NULL DEFAULT 1 COMMENT 'Active( 0 - Inactive, 1 - Active )',
  `replaygain` int(11) NULL DEFAULT 0 COMMENT 'ReplayGain Value',
  `explicit` int(1) NULL DEFAULT 0 COMMENT 'Explicit (0 - No, 1 - Yes)',
  `isFL` int(1) NULL DEFAULT 0 COMMENT 'is Full License\n0: not fl\n1: is fl',
  `dlanguage` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Default Language',
  `md5` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'the md5 of the track file',
  `isCustom` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'to indicate if this song can only be used for some users\n0: all can use , 1: only selected users can use',
  `preview` int(9) NOT NULL DEFAULT 0 COMMENT 'the num of preview (used in ibomv2 musicbom plus)',
  `like` int(9) NOT NULL DEFAULT 0 COMMENT 'the num of like (used in ibomv2 musicbom plus)',
  `insert` int(9) NOT NULL DEFAULT 0 COMMENT 'the num of insert (used in ibomv2 musicbom plus)',
  `collect` int(9) NOT NULL DEFAULT 0 COMMENT 'the num of collected (used in ibomv2 musicbom plus)',
  `mbp_cost` int(3) NOT NULL DEFAULT 1,
  `youtube` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'the youtube link of this track',
  `brief_desc` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'the brief desciption of the the video',
  `detail_desc` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'the detail desciption of the the video',
  `startdate` date NULL DEFAULT NULL COMMENT 'the start date this track can be used',
  `enddate` date NULL DEFAULT NULL COMMENT 'the end date this track can be used',
  `domain` smallint(2) NOT NULL DEFAULT 1 COMMENT '# we dont bind to license becasue license can change\nindicatethe domain this track locates, default is 1',
  PRIMARY KEY (`track_id`, `isrc`) USING BTREE,
  UNIQUE INDEX `track_id_UNIQUE`(`track_id`) USING BTREE,
  INDEX `trackidisrc`(`track_id`, `isrc`) USING BTREE,
  INDEX `track_order`(`year`, `month`) USING BTREE,
  INDEX `track_FL`(`track_id`, `isFL`) USING BTREE,
  INDEX `track_created_date`(`created_date`) USING BTREE,
  INDEX `idx_search_active_custom_dates`(`active`, `isCustom`, `startdate`, `enddate`) USING BTREE,
  INDEX `idx_search_active_custom_fl`(`active`, `isCustom`, `isFL`) USING BTREE,
  INDEX `idx_search_track_status`(`track_id`, `active`, `isCustom`) USING BTREE,
  INDEX `idx_track_custom_active`(`isCustom`, `active`, `track_id`) USING BTREE,
  INDEX `idx_track_lang_filter`(`isCustom`, `active`, `dlanguage`, `track_id`) USING BTREE,
  INDEX `idx_track_bitrate_filter`(`isCustom`, `active`, `bitrate`, `track_id`) USING BTREE,
  INDEX `idx_track_lang_bitrate`(`isCustom`, `active`, `dlanguage`, `bitrate`, `track_id`) USING BTREE,
  INDEX `idx_track_domain_filter`(`domain`, `isCustom`, `active`, `track_id`) USING BTREE,
  FULLTEXT INDEX `trackname`(`trackname`)
) ENGINE = InnoDB AUTO_INCREMENT = 4249948 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'track database' ROW_FORMAT = Compact;

CREATE TABLE `avs`.`artist_name`  (
  `id` int(15) NOT NULL AUTO_INCREMENT COMMENT 'Unique ID',
  `artist_id` int(15) NOT NULL COMMENT 'Artist ID',
  `artist_name` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'Artist Name',
  `language` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Language',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id_UNIQUE`(`id`) USING BTREE,
  INDEX `artistname_id_idx`(`artist_id`) USING BTREE,
  FULLTEXT INDEX `artistname_fulltext`(`artist_name`),
  CONSTRAINT `artistname_id` FOREIGN KEY (`artist_id`) REFERENCES `avs`.`artist` (`artist_id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE = InnoDB AUTO_INCREMENT = 5945710 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'Artist Name' ROW_FORMAT = Compact;

CREATE TABLE `avs`.`m3_artist_link`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `track_id` int(15) NOT NULL,
  `artist_id` int(15) NOT NULL,
  `role_id` int(11) NOT NULL,
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL DEFAULT 1000010001,
  `updated_date` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`track_id`, `artist_id`, `role_id`) USING BTREE,
  UNIQUE INDEX `id_UNIQUE`(`id`) USING BTREE,
  INDEX `m3_artist_link_idx`(`id`, `track_id`, `artist_id`, `role_id`) USING BTREE,
  INDEX `m3_artist_track_idx`(`track_id`) USING BTREE,
  INDEX `m3_artist_artist_idx`(`artist_id`) USING BTREE,
  INDEX `m3_artist_role_idx`(`role_id`) USING BTREE,
  INDEX `idx_search_track_artist_role`(`track_id`, `artist_id`, `role_id`) USING BTREE,
  INDEX `idx_search_artist_role`(`artist_id`, `role_id`) USING BTREE,
  INDEX `idx_search_role_track`(`role_id`, `track_id`) USING BTREE,
  INDEX `idx_m3_artist_role_track`(`track_id`, `role_id`, `artist_id`) USING BTREE,
  INDEX `idx_artist_link_track_role`(`track_id`, `role_id`, `artist_id`) USING BTREE,
  INDEX `idx_artist_link_artist`(`artist_id`, `role_id`, `track_id`) USING BTREE,
  INDEX `idx_artist_link_role`(`role_id`, `track_id`, `artist_id`) USING BTREE,
  CONSTRAINT `m3_artist_artist` FOREIGN KEY (`artist_id`) REFERENCES `avs`.`artist` (`artist_id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `m3_artist_role` FOREIGN KEY (`role_id`) REFERENCES `avs`.`role` (`role_id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `m3_artist_track` FOREIGN KEY (`track_id`) REFERENCES `avs`.`track` (`track_id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE = InnoDB AUTO_INCREMENT = 45222749 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'the new artist link of m3' ROW_FORMAT = Compact;

CREATE TABLE `avs`.`track_name`  (
  `id` int(15) NOT NULL AUTO_INCREMENT,
  `track_id` int(15) NOT NULL,
  `track_name` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `language` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `subtitle` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id_UNIQUE`(`id`) USING BTREE,
  INDEX `trackid_idx`(`track_id`) USING BTREE,
  INDEX `idx_track_name_track`(`track_id`) USING BTREE,
  FULLTEXT INDEX `trackname_fulltext`(`track_name`),
  CONSTRAINT `trackid` FOREIGN KEY (`track_id`) REFERENCES `avs`.`track` (`track_id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE = InnoDB AUTO_INCREMENT = 14551027 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'Track Name' ROW_FORMAT = Compact;

CREATE TABLE `avs`.`artist_name`  (
  `id` int(15) NOT NULL AUTO_INCREMENT COMMENT 'Unique ID',
  `artist_id` int(15) NOT NULL COMMENT 'Artist ID',
  `artist_name` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'Artist Name',
  `language` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Language',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id_UNIQUE`(`id`) USING BTREE,
  INDEX `artistname_id_idx`(`artist_id`) USING BTREE,
  FULLTEXT INDEX `artistname_fulltext`(`artist_name`),
  CONSTRAINT `artistname_id` FOREIGN KEY (`artist_id`) REFERENCES `avs`.`artist` (`artist_id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE = InnoDB AUTO_INCREMENT = 5945710 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'Artist Name' ROW_FORMAT = Compact;

CREATE TABLE `avs`.`m3_album_link`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `track_id` int(15) NOT NULL COMMENT 'the track id',
  `album_id` int(15) NOT NULL COMMENT 'the album id',
  `created_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(10) NOT NULL DEFAULT 1000010001,
  PRIMARY KEY (`track_id`, `album_id`) USING BTREE,
  UNIQUE INDEX `id_UNIQUE`(`id`) USING BTREE,
  INDEX `m3_album_link_idx`(`id`, `track_id`, `album_id`) USING BTREE,
  INDEX `m3_album_track_idx`(`track_id`) USING BTREE,
  INDEX `m3_album_album_idx`(`album_id`) USING BTREE,
  INDEX `idx_search_track_album`(`track_id`, `album_id`) USING BTREE,
  INDEX `idx_search_album_track`(`album_id`, `track_id`) USING BTREE,
  CONSTRAINT `m3_album_album` FOREIGN KEY (`album_id`) REFERENCES `avs`.`album` (`album_id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `m3_album_track` FOREIGN KEY (`track_id`) REFERENCES `avs`.`track` (`track_id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE = InnoDB AUTO_INCREMENT = 4223156 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'new album link of m3' ROW_FORMAT = Compact;

CREATE TABLE `avs`.`artist`  (
  `artist_id` int(15) NOT NULL AUTO_INCREMENT,
  `artist1` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `artist2` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `grp` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Group (Male, Female, Group, etc)',
  `role` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Role (MainArtist, AssociatedPerformer, Composer, Producer, Recording Engineer)',
  `comment` varchar(5120) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `usagecount` int(5) NULL DEFAULT 0,
  `cover` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'the filelocation of the phot of the artist , used in ibom musicbom+',
  `lang` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'the default lang this artist uses for the albums and tracks\n\nzh-hant : tradional chinese',
  PRIMARY KEY (`artist_id`) USING BTREE,
  UNIQUE INDEX `artist_id_UNIQUE`(`artist_id`) USING BTREE,
  INDEX `artistLang`(`lang`) USING BTREE,
  INDEX `idx_artist_name`(`artist1`(255), `artist_id`) USING BTREE,
  INDEX `idx_artist_id`(`artist_id`, `artist1`(255)) USING BTREE,
  FULLTEXT INDEX `artistName`(`artist1`)
) ENGINE = InnoDB AUTO_INCREMENT = 1254196 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'Artist database' ROW_FORMAT = Compact;

CREATE TABLE `avs`.`m3_album_name`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `album_id` int(11) NOT NULL,
  `album_name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `language` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `m3_album_name_album_name_idx`(`album_id`) USING BTREE,
  FULLTEXT INDEX `albumname_fulltext`(`album_name`),
  CONSTRAINT `m3_album_name_album_id` FOREIGN KEY (`album_id`) REFERENCES `avs`.`album` (`album_id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE = InnoDB AUTO_INCREMENT = 764999 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'to store other name of the album' ROW_FORMAT = Compact;