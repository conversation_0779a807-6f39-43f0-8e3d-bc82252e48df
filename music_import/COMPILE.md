# 编译指南

## 🔧 编译要求

### 必需依赖

1. **Rust 工具链**
   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   ```

2. **Protocol Buffers 编译器 (protoc)**
   
   **Ubuntu/Debian:**
   ```bash
   sudo apt-get update
   sudo apt-get install protobuf-compiler
   ```
   
   **macOS:**
   ```bash
   brew install protobuf
   ```
   
   **Windows:**
   ```bash
   # 使用 Chocolatey
   choco install protoc
   
   # 或者手动下载
   # 从 https://github.com/protocolbuffers/protobuf/releases 下载
   # 解压并添加到 PATH
   ```

3. **其他系统依赖**
   
   **Ubuntu/Debian:**
   ```bash
   sudo apt-get install pkg-config libssl-dev
   ```
   
   **macOS:**
   ```bash
   # 通常不需要额外依赖
   ```
   
   **Windows:**
   ```bash
   # 确保安装了 Visual Studio Build Tools
   ```

## 🚀 编译步骤

### 1. 验证环境
```bash
# 检查 Rust
rustc --version
cargo --version

# 检查 protoc
protoc --version

# 检查项目文件
ls proto/
```

### 2. 编译项目
```bash
# Debug 版本
cargo build

# Release 版本
cargo build --release

# 详细输出（用于调试）
cargo build --verbose
```

### 3. 运行测试
```bash
cargo test
```

## 🐛 常见问题

### 问题1: protoc 未找到
```
Error: Could not find `protoc`
```

**解决方案:**
1. 安装 protobuf-compiler (见上面的安装指令)
2. 确保 protoc 在 PATH 中
3. 或设置 PROTOC 环境变量

### 问题2: OpenSSL 相关错误
```
error: failed to run custom build command for `openssl-sys`
```

**解决方案:**
```bash
# Ubuntu/Debian
sudo apt-get install libssl-dev

# macOS
brew install openssl

# Windows
# 使用 vcpkg 或预编译的 OpenSSL
```

### 问题3: 链接错误
```
error: linking with `cc` failed
```

**解决方案:**
```bash
# Ubuntu/Debian
sudo apt-get install build-essential

# macOS
xcode-select --install
```

## 🔍 调试编译问题

### 启用详细输出
```bash
RUST_LOG=debug cargo build --verbose
```

### 检查依赖
```bash
cargo tree
```

### 清理并重新编译
```bash
cargo clean
cargo build
```

## 🌐 GitHub Actions

项目配置了 GitHub Actions 自动编译，支持：
- Windows (x64)
- Linux (x64)  
- macOS (x64)

如果 GitHub Actions 失败：
1. 检查是否有新的依赖需要安装
2. 查看构建日志中的具体错误
3. 本地复现问题并测试修复

## 📋 环境变量

编译时可能需要的环境变量：

```bash
# 如果 protoc 不在 PATH 中
export PROTOC=/path/to/protoc

# 如果需要特定的 OpenSSL
export OPENSSL_DIR=/usr/local/opt/openssl

# Rust 编译选项
export RUSTFLAGS="-C target-cpu=native"
```

## ✅ 验证编译结果

编译成功后，验证二进制文件：

```bash
# 检查文件
ls -la target/release/music_import*

# 测试运行
./target/release/music_import --help

# 检查依赖
ldd target/release/music_import  # Linux
otool -L target/release/music_import  # macOS
```

## 🎯 性能优化

### Release 编译优化
```bash
# 最大优化
RUSTFLAGS="-C target-cpu=native" cargo build --release

# 减小二进制大小
cargo build --release --config profile.release.strip=true
```

### 并行编译
```bash
# 使用多核编译
cargo build --release -j $(nproc)
```

## 📞 获取帮助

如果遇到编译问题：
1. 查看本文档的常见问题部分
2. 检查 GitHub Issues
3. 确保所有依赖都已正确安装
4. 尝试在干净的环境中编译
