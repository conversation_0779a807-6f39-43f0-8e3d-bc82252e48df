﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;
using UMGImport.util;

namespace UMGImport.Class
{
    class Party
    {
        // the party reference, P1 P2 etc
        private string reference;

        // names of the party
        private List<Name> names = new List<Name>();

        public Party(XmlNode party)
        {
            reference = party.getText("PartyReference");

            foreach (XmlNode name in party.SelectNodes("PartyName")) {
                string lang = name.getAttr("LanguageAndScriptCode");
                string fullName = name.getText("FullName");

                // if no lang and script code, consider as formal
                names.Add(new Name(fullName, lang, lang.isEmpty()));
            }
        }

        public string getReference()
        {
            return reference;
        }

        public List<Name> getNames()
        {
            return names;
        }
    }
}
