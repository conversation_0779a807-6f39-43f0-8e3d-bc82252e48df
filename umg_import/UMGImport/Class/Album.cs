﻿using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Xml;
using System;
using System.IO;
using UMGImport.util;

namespace UMGImport.Class
{
    class Album
    {
        public bool isCompleted = false;
        public bool isUpdate = false;
        public bool success = true;

        private static Object _lock = new Object();

        public string BASE_PATH;
        private string COVER_PATH;
        private string DB_COVER_PATH;
        public string relType;

        public long album_id = -1;
        private string msgType = "";
        private string grid = "";
        private string icpn = "";

        private string duration = "";
        private string copyright = "";

        private string relDate = "";
        public int year = 0;
        public int month = 0;

        private string labelName;

        // for logging the reading result
        private List<string> result = new List<string>();
        private List<Title> titles = new List<Title>();
        private Title formalTitle = null;

        private List<Track> tracks = new List<Track>();

        private Regex regex = new Regex(@"PT(?<hour>\d+)H(?<minute>\d+)M(?<second>\d+)(\.0+)?S");
        private Regex regex2 = new Regex(@"PT(?<minute>\d+)M(?<second>\d+)(\.0+)?S");
        private Match match;

        private Dictionary<string, long> dbresult;
        private List<object> _r;

        private string displayArtist;

        private string log_path;
        private string log_success;
        private string log_update;

        public string startDate = "";
        public string endDate = "";
        public List<string> territoryCode = new List<string>();

        private List<Party> parties;

        public Album(string path , string xmlPath)
        {
            BASE_PATH = path + '/';
            log_path = umgimport.LOG_LOCATION +
                Util.dirname(xmlPath).Replace("\\", "/").Replace(umgimport.FOLDER_LOCATION, string.Empty) + '/';
            log_success = log_path + "success.dat";
            log_update = log_path + "update.dat";

            if (!Directory.Exists(log_path))
            {
                Directory.CreateDirectory(log_path);
            }
        }

        public void setParty(List<Party> party)
        {
            this.parties = party;
        }

        // should be useless in UMG, coz no R0 in deal list
        public void readDealInformation(XmlNode deal)
        {
            return;
            XmlNode dealTerms = deal.SelectSingleNode("DealTerms");
            if (dealTerms == null)
            {
                throw new Exception("MetadataMissing");
            }

            startDate = dealTerms.getText("ValidityPeriod/StartDate");
            endDate = dealTerms.getText("ValidityPeriod/EndDate");

            foreach (XmlNode territory in dealTerms.SelectNodes("TerritoryCode"))
            {
                string _t = territory.getText();
                if (!String.IsNullOrEmpty(_t))
                {
                    territoryCode.Add(_t);
                }
            }
        }

        public void getCover(XmlNode res)
        {
            XmlNodeList covers = res.SelectNodes("Image");
            foreach (XmlNode c in covers)
            {
                if (c.getText("Type") == "FrontCoverImage")
                {
                    //XmlNode file = c.SelectSingleNode("ImageDetailsByTerritory/TechnicalImageDetails/File");
                    XmlNode file = c.SelectSingleNode("TechnicalDetails/File");


                    if (file == null)
                    {
                        // this is update , as they said no image file for update
                        isUpdate = true;
                        return;
                    }

                    Dictionary<string, object> _result = Util.checkFileExist(file, BASE_PATH);

                    string path = (string)_result["path"];
                    if ((bool)_result["success"])
                    {
                        COVER_PATH = path;
                        DB_COVER_PATH = path.Replace(umgimport.FOLDER_LOCATION, umgimport.NAS_DIR);
                        if (Util.uploadFileBySFTP(path, DB_COVER_PATH))
                        {
                            Util.writeToWholeLog("Upload Cover Success");
                        }
                        else
                        {
                            Util.writeToWholeLog("Upload Cover Fail", true);
                        }
                    }
                    else
                    {
                        success = false;
                        string msg = (string)_result["msg"];

                        Util.writeToWholeLog("Cover fail :" + path +
                            " with reason : " + msg, true);

                        result.Add("[ERROR] " + path + " fail because " + msg);
                    }

                }
            }
        }

        public void getAlbum(XmlNode rel)
        {
            XmlNodeList releases = rel.SelectNodes("Release");
            foreach (XmlNode r in releases)
            {
                if (r.getText("ReleaseReference") == "R0")
                {
                    relType = r.getText("ReleaseType");

                    XmlNode releaseID = r.SelectSingleNode("ReleaseId");
                    icpn = releaseID.getText("ICPN");

                    relDate = r.getText("OriginalReleaseDate");
                    if (relDate.isEmpty())
                    {
                        relDate = r.getText("ReleaseDate");
                    }

                    if (r.SelectSingleNode("DisplayTitle") != null)
                    {
                        setRefTitle(r.SelectSingleNode("DisplayTitle"));
                    }
                    else if (r.SelectSingleNode("ReferenceTitle") != null)
                    {
                        setRefTitle(r.SelectSingleNode("ReferenceTitle"));
                    }

                    foreach (XmlNode detail in r.SelectNodes("ReleaseDetailsByTerritory"))
                    {
                        if (detail.getText("TerritoryCode") == "HK")
                        {
                            relDate = detail.getText("ReleaseDate");
                            if (startDate.isEmpty())
                            {
                                startDate = relDate;
                            }

                            if (!string.IsNullOrEmpty(relDate))
                            {
                                string[] d = relDate.Split('-');
                                year = int.Parse(d[0]);
                                month = int.Parse(d[1]);
                            }

                            if (string.IsNullOrEmpty(displayArtist))
                            {
                                displayArtist = detail.getText("DisplayArtistName");
                            }

                            if (!string.IsNullOrEmpty(detail.getText("LabelName")))
                            {
                                labelName = detail.getText("LabelName");
                            }
                            
                        }
                        else if (detail.getText("TerritoryCode") == "Worldwide")
                        {
                            if (string.IsNullOrEmpty(displayArtist))
                            {
                                displayArtist = detail.getText("DisplayArtistName");
                            }

                            if (!string.IsNullOrEmpty(detail.getText("LabelName")))
                            {
                                labelName = detail.getText("LabelName");
                            }

                        }

                        setTitle(detail.SelectNodes("Title"));

                        if (string.IsNullOrEmpty(copyright))
                        {
                            copyright = detail.getText("PLine/PLineText");
                        }
                    }

                    // if not for hk , use the worldwide
                    if (year == 0)
                    {
                        foreach (XmlNode detail in r.SelectNodes("ReleaseDetailsByTerritory"))
                        {
                            if (detail.getText("TerritoryCode") == "Worldwide")
                            {
                                relDate = detail.getText("ReleaseDate");
                                if (startDate.isEmpty())
                                {
                                    startDate = relDate;
                                }

                                if (!string.IsNullOrEmpty(relDate))
                                {
                                    string[] d = relDate.Split('-');
                                    year = int.Parse(d[0]);
                                    month = int.Parse(d[1]);
                                }

                                if (string.IsNullOrEmpty(displayArtist))
                                {
                                    displayArtist = detail.getText("DisplayArtistName");
                                }

                                if (!string.IsNullOrEmpty(detail.getText("LabelName")))
                                {
                                    labelName = detail.getText("LabelName");
                                }
                            }

                            setTitle(detail.SelectNodes("Title"));
                        }
                    }

                    match = regex.Match(r.getText("Duration"));

                    if (match.Success)
                    {
                        GroupCollection result = match.Groups;

                        duration = int.Parse(result["hour"].Value).ToString("00") + ':' +
                                    int.Parse(result["minute"].Value).ToString("00") + ':' +
                                    int.Parse(result["second"].Value).ToString("00");

                    }
                    else
                    {
                        // if no hour , check minute , eg PT27M21.000S
                        match = regex2.Match(r.getText("Duration"));

                        if (match.Success)
                        {
                            GroupCollection result = match.Groups;
                            duration = "00:" +
                                    int.Parse(result["minute"].Value).ToString("00") + ':' +
                                    int.Parse(result["second"].Value).ToString("00");
                        }
                        else
                        {
                            Util.writeToWholeLog("Album Duration Wrong Format", true);
                            result.Add("[Error] Album Duration Wrong Format");
                        }
                        
                    }

                    break;
                }
            }
        }

        public void getTracks(XmlNode dealList, XmlNode relList, XmlNode res)
        {
            int disk_no = 1;
            int track_no = 1;

            /*** Get Reuired Node to avoid keep reading ***/
            // get the Release in ReleaseList, not the main release, to get the the A1, A2 etc
            List<XmlNode> releaseList = new List<XmlNode>();
            // we need the grouping information here, say disc, track no etc
            XmlNodeList releaseDetail = null;

            foreach (XmlNode release in relList.SelectNodes("TrackRelease"))
            {
                releaseList.Add(release);
            }

            /*foreach (XmlNode release in relList.SelectNodes("Release"))
            {
                if (release.getAttr("IsMainRelease") == "true")
                {
                    // we get the grouping from main release
                    releaseDetail = release.SelectNodes("ReleaseDetailsByTerritory");
                }
                else
                {
                    // we get the release node
                    releaseList.Add(release);
                }
            }
            if (releaseList.Count == 0 || releaseDetail == null)
            {
                throw new Exception("MetadataMissing: Release List/ Release Detail");
            }

            XmlNodeList resGroup = null;
            foreach (XmlNode territory in releaseDetail)
            {
                string _t = territory.getText("TerritoryCode");
                if (_t == "Worldwide" || _t == "HK")
                {
                    resGroup = territory.SelectNodes("ResourceGroup/ResourceGroup");
                    break;
                }
            }*/

            XmlNodeList resGroup = relList.SelectNodes("Release/ResourceGroup/ResourceGroups");

            if (resGroup == null)
            {
                throw new Exception("MetadataMissing: ResourceGroup/ResourceGroup");
            }

            // get the soundrecording in resourecList
            XmlNodeList recordings = res.SelectNodes("SoundRecording");
            /*** End of reading nodes ***/

            foreach (XmlNode releaseDeal in dealList.SelectNodes("ReleaseDeal"))
            {
                string sd = String.Empty;
                string ed = String.Empty;

                // multiple deals
                foreach (XmlNode _d in releaseDeal.SelectNodes("Deal"))
                {
                    bool isFound = false;
                    foreach (XmlNode type in _d.SelectNodes("DealTerms/CommercialModelType"))
                    {
                        if (type.getText() == "PayAsYouGoModel" || type.getText() == "SubscriptionModel") {
                            isFound = true;
                            break;
                        }
                    }

                    if (isFound)
                    {
                        XmlNode validity = _d.SelectSingleNode("DealTerms/ValidityPeriod");
                        if (validity == null)
                        {
                            throw new Exception("MetadataMissing: Deal/DealTerms/ValidityPeriod");
                        }

                        sd = validity.getText("StartDate");
                        ed = validity.getText("EndDate");
                        break;
                    }
                }
                

                XmlNodeList dealRef = releaseDeal.SelectNodes("DealReleaseReference");

                foreach (XmlNode drf in dealRef)
                {
                    string reference = drf.getText();
                    if (reference.isEmpty())
                    {
                        throw new Exception("MetadataMissing: deal reference R0/R1 etc");
                    }

                    // the album one, we have read it
                    if (reference == "R0")
                    {
                        continue;
                    }

                    // we have a reference code(R1, R2 etc), iterate the resource list and get the resource
                    Track t = null;

                    foreach (XmlNode _r in releaseList)
                    {
                        string labelRef = _r.getText("ReleaseLabelReference");
                        if (!labelRef.isEmpty())
                        {
                            foreach (Party party in parties)
                            {
                                if (party.getReference().Equals(labelRef))
                                {
                                    labelName = party.getNames()[0].name;
                                    break;
                                }
                            }
                        }

                        if (labelName.isEmpty())
                        {
                            labelName = "UMG New";
                        }

                        if (_r.getText("ReleaseReference") == reference)
                        {
                            // the A1, A2 etc
                            //string resRef = _r.getText("ReleaseResourceReferenceList/ReleaseResourceReference");
                            string resRef = _r.getText("ReleaseResourceReference");

                            if (resRef.isEmpty())
                            {
                                throw new Exception("MetadataMissing: ReleaseResourceReference A0 A1 etc");
                            }

                            // get the disk no and track no from group
                            foreach (XmlNode group in resGroup)
                            {
                                // get track number
                                if (!int.TryParse(group.getText("SequenceNumber"), out disk_no))
                                {
                                    throw new Exception("MetadataMissing: Disk Number");
                                }

                                foreach (XmlNode item in group.SelectNodes("ResourceGroupContentItem"))
                                {
                                    if (item.getText("ReleaseResourceReference") == resRef)
                                    {
                                        if (!int.TryParse(item.getText("SequenceNumber"), out track_no))
                                        {
                                            throw new Exception("MetadataMissing: Track number");
                                        }
                                    }
                                }
                            }

                            // we have the resource ref (A1,A2) and disk no/track no
                            // create track
                            foreach (XmlNode track in recordings)
                            {
                                if (track.getText("ResourceReference") == resRef)
                                {
                                   // t = new Track(BASE_PATH, track.getText("SoundRecordingId/ISRC"), this);
                                    t = new Track(BASE_PATH, track.getText("ResourceId/ISRC"), this);

                                    t.disc = disk_no;
                                    t.track = track_no;
                                    t.startDate = sd;
                                    t.endDate = ed;

                                    //XmlNodeList detail = track.SelectNodes("SoundRecordingDetailsByTerritory");

                                    if (track.SelectSingleNode("DisplayTitle") != null)
                                    {
                                        t.setRefTitle(track.SelectSingleNode("DisplayTitle"));
                                    }
                                    else if (track.SelectSingleNode("ReferenceTitle") != null)
                                    {
                                        t.setRefTitle(track.SelectSingleNode("ReferenceTitle"));
                                    }

                                    bool hkFound = false;

                                    //foreach (XmlNode _d in detail)
                                    //{
                                    //t.setTitle(track.SelectNodes("Title"));
                                        t.setTitle(track.SelectNodes("DisplayTitle"));

                                        if (!string.IsNullOrEmpty(track.getText("PLine/PLineText")))
                                        {
                                            t.setCP(track.getText("PLine/PLineText"));
                                        }

                                        if (!string.IsNullOrEmpty(track.getText("ParentalWarningType")))
                                        {
                                            t.setExplicit(track.getText("ParentalWarningType") != "NoAdviceAvailable" && 
                                                track.getText("ParentalWarningType") != "NotExplicit");
                                        }

                                        t.setSoundFile(track.SelectNodes("TechnicalDetails"));

                                        //if (_d.getText("TerritoryCode") == "HK")
                                        //{
                                            if (track.SelectNodes("DisplayArtist").Count > 0)
                                            {
                                                hkFound = true;
                                                t.setArtist(track.SelectNodes("DisplayArtist"), parties);
                                                t.setContri(track.SelectNodes("Contributor"), parties);
                                                //break;
                                            }
                                    //}
                                    //}

                                    // if no name for hk , do it again
                                    /*if (!hkFound)
                                    {
                                        foreach (XmlNode _d in detail)
                                        {
                                            t.setArtist(_d.SelectNodes("DisplayArtist"));
                                            t.setContri(_d.SelectNodes("ResourceContributor"));
                                            t.setSoundFile(_d.SelectNodes("TechnicalSoundRecordingDetails"));
                                            if (!string.IsNullOrEmpty(_d.getText("PLine/PLineText")))
                                            {
                                                t.setCP(_d.getText("PLine/PLineText"));
                                            }

                                            if (!string.IsNullOrEmpty(_d.getText("ParentalWarningType")))
                                            {
                                                t.setExplicit(_d.getText("ParentalWarningType") != "NoAdviceAvailable" &&
                                                                 _d.getText("ParentalWarningType") != "NotExplicit");
                                            }   
                                        }
                                    }*/

                                    t.setLabel(labelName);

                                    t.setTrackNo(track.getText("ResourceReference"));

                                    this.tracks.Add(t);

                                }
                            }
                        }
                    }
                }
            }
        }

        private void setRefTitle(XmlNode t)
        {
            string title = t.getText("TitleText");

            if (!string.IsNullOrEmpty(title))
            {
                formalTitle = new Title(new Name(title, null));
                this.titles.Add(formalTitle);
            }
        }

        private void setTitle(XmlNodeList titles)
        {
            if (formalTitle == null)
            {
                foreach (XmlNode t in titles)
                {
                    if (t.getAttr("TitleType") == "FormalTitle")
                    {

                        Title temp = new Title(new Name(t.getText("TitleText"), t.getNullableAttr("LanguageAndScriptCode")));

                        if (formalTitle == null)
                        {
                            formalTitle = temp;
                        }
                        else
                        {
                            // formal title has been set , check priority
                            if (Util.checkPriority(formalTitle.lang, temp.lang))
                            {
                                formalTitle = temp;
                            }
                        }

                        this.titles.Add(temp);
                        // we do not break because we want to get the formal title with greatest priority
                    }
                }
            }

            foreach (XmlNode t in titles)
            {
                if (t.getAttr("TitleType") != "FormalTitle")
                {
                    Title temp = new Title(new Name(t.getText("TitleText"), t.getNullableAttr("LanguageAndScriptCode")));

                    // if running above still cannot find formal title
                    if (formalTitle == null)
                    {
                        formalTitle = temp;
                        this.titles.Add(temp);
                    }
                    else
                    {
                        // formal title has been set , check priority
                        // add if the lang code / title not the same
                        if (temp.lang != formalTitle.lang ||
                            temp.name != formalTitle.name)
                        {
                            this.titles.Add(temp);
                        }
                    }
                }
            }

        }

        public void displayAlbum()
        {
            addAlbumToDB();

            string log = log_path + "log.txt";

            result.Add("Album Name: " + formalTitle.name);
            result.Add("MessageType: " + msgType);
            result.Add("Release Type: " + relType);
            result.Add("Release Date: " + relDate);
            result.Add("Total Duration: " + duration);
            result.Add("grid: " + grid);
            result.Add("icpn: " + icpn);
            result.Add("coverPath: " + COVER_PATH);
            result.Add("copyright: " + copyright);
            result.Add(Environment.NewLine);
            result.Add(Environment.NewLine);

            int count = 1;

            if (album_id != -1)
            {
                foreach (Track t in tracks)
                {
                    success = t.addTrackToDB(album_id) && success;
                    result.Add("Track " + (count++) + " : ");
                    result.AddRange(t.displayTrack(album_id));
                }
            }

            isCompleted = true;
            Util.writeFile(log, result);
        }

        private void addAlbumToDB()
        {
            Dictionary<string, object> para = new Dictionary<string, object>
            {
                { "`album_name`" , formalTitle.name},
                { "`grid`" , grid},
                { "`icpn`" , icpn}
            };

            // seperate For update, to avoid updating empty value
            if (!DB_COVER_PATH.isEmpty())
            {
                para.Add("`cover_link`", DB_COVER_PATH);
            }

            if (!relDate.isEmpty())
            {
                para.Add("`releasedate`", relDate);
            }

            if (!duration.isEmpty())
            {
                para.Add("`duration`", duration);
            }

            if (!startDate.isEmpty())
            {
                para.Add("`startdate`", startDate);
            }

            if (!endDate.isEmpty())
            {
                para.Add("`enddate`", endDate);
            }
            // end of update

            Dictionary<string, object> where = new Dictionary<string, object>
            {
                { "@W1" , icpn},
                { "@W2" , duration}
            };

            lock (_lock)
            {

                string sql = "select album_id from album where icpn = @W1 and duration = @W2";
                DB.shared.select(sql, where, dr =>
                {
                    if (dr.HasRows)
                    {
                        // album exists
                        dr.Read();
                        album_id = long.Parse(dr["album_id"].ToString());

                        where = new Dictionary<string, object>
                       {
                            { "`album_id`" , album_id}
                       };

                        _r = Util.getUpdateSql("`album`", para, where);

                        DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                        Util.writeToWholeLog("Album Updated : " + album_id + ' ' + formalTitle.name);
                    }
                    else
                    {
                        if (duration.isEmpty() || DB_COVER_PATH.isEmpty())
                        {
                            Util.writeToWholeLog("Inserting new album with empty data", true);
                            return;
                        }

                        _r = Util.getInsertSql("`album`", para);

                        dbresult = DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                        if (dbresult["success"] == DB.SUCCESS)
                        {
                            long id = dbresult["inert_row_id"];
                            album_id = id;

                            Util.writeToWholeLog("Album Inserted : " + album_id + ' ' + formalTitle.name);
                        }

                    }
                });

                if (album_id == -1)
                {
                    return;
                }

                // insert the titles
                foreach (Title t in titles)
                {
                    where = new Dictionary<string, object>
                {
                    { "@W1" , t.name },
                    { "@W2" , album_id }
                };

                    sql = "select 1 from `m3_album_name` where album_name = @W1 and album_id = @W2 and `language` is null";

                    if (!string.IsNullOrEmpty(t.lang))
                    {
                        sql = "select 1 from `m3_album_name` where album_name = @W1 and album_id = @W2 and `language` = @W3";
                        where.Add("@W3", t.lang);
                    }

                    DB.shared.select(sql, where, dr =>
                    {

                        if (dr.HasRows)
                        {
                            // album name already exists , do nth
                        }
                        else
                        {
                            para = new Dictionary<string, object>
                           {
                                { "`album_id`" , album_id },
                                { "`album_name`" , t.name },
                                { "`language`" , t.lang }
                           };

                            _r = Util.getInsertSql("`m3_album_name`", para);

                            DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);

                        }
                    });

                }

            }
        }

        public string getResult()
        {
            return '"' + formalTitle.name + "\" by " + displayArtist;
        }

        public void writeSuccess()
        {
            using (System.IO.StreamWriter file =
                    new System.IO.StreamWriter(log_success, true))
            {
                file.WriteLine(String.Empty);
            }
        }

        public void writeUpdate()
        {
            using (
                System.IO.StreamWriter file =
                    new System.IO.StreamWriter(log_update, true))
            {
                file.WriteLine(String.Empty);
            }
        }

        public bool isAlreadySuccess()
        {
            return File.Exists(log_success);
        }

        public bool isAlreadyUpdate()
        {
            return File.Exists(log_update);
        }

    }
}

