﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace UMGImport.util
{
    public static class StringExtension
    {

        public static String empty(this string s)
        {
            return s == null ? "" : s;
        }

        public static String PregReplace(this String input, string[] pattern, string[] replacements)
        {
            if (replacements.Length != pattern.Length)
                throw new ArgumentException("Replacement and Pattern Arrays must be balanced");

            for (var i = 0; i < pattern.Length; i++)
            {
                input = Regex.Replace(input, pattern[i], replacements[i]);
            }

            return input;
        }

        public static Boolean isEmpty(this string s)
        {
            return String.IsNullOrEmpty(s);
        }

    }
}
