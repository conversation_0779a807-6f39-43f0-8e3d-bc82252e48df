﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0" /></startup><system.data>
    <DbProviderFactories>
      <remove invariant="MySql.Data.MySqlClient" />
      <add name="MySQL Data Provider" invariant="MySql.Data.MySqlClient" description=".Net Framework Data Provider for MySQL" type="MySql.Data.MySqlClient.MySqlClientFactory, MySql.Data, Version=*******, Culture=neutral, PublicKeyToken=c5687fc88969c44d" />
    </DbProviderFactories>
  </system.data></configuration>
