﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WarnerImport.Class
{
    class Title : Name
    {

        public string sub = null;

        public Title(Name name, string sub = null) : base(name.name, name.lang)
        {
            if (!String.IsNullOrEmpty(sub))
            {
                this.sub = sub.Trim().Normalize(NormalizationForm.FormKC).Replace("’", "'");
            }
        }

        override public void display()
        {
            Console.WriteLine(name + " : " + lang + " : " + sub);
        }

    }
}
