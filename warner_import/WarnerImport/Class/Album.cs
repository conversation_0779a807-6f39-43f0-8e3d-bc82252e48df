﻿using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Xml;
using System;
using System.IO;
using WarnerImport.util;
using MySql.Data.MySqlClient;

namespace WarnerImport.Class
{
    class Album
    {
        public bool isCompleted = false;
        public bool isUpdate = false;
        public bool success = true;
        public bool takeDownSuccess = false;

        private static Object _lock = new Object();

        public string BASE_PATH;
        private string COVER_PATH;
        private string DB_COVER_PATH;
        public string relType;

        public long album_id = -1;
        private string msgType = "";
        private string grid = "";
        private string icpn = "";

        private string duration = "";
        private string copyright = "";

        private string relDate = "";
        public int year = 0;
        public int month = 0;

        // for logging the reading result
        private List<string> result = new List<string>();
        private List<Title> titles = new List<Title>();
        private Title formalTitle = null;

        private List<Track> tracks = new List<Track>();

        private Regex regex = new Regex(@"PT(?<hour>\d+)H(?<minute>\d+)M(?<second>\d+)S");
        private Match match;

        private Dictionary<string, long> dbresult;
        private List<object> _r;

        private string displayArtist;

        private string log_path;
        private string log_success;
        private string log_update;

        public string startDate = "";
        public string endDate = "";
        public List<string> territoryCode = new List<string>();

        public Album(string path, string update, string xmlPath)
        {
            BASE_PATH = path + '/';
            msgType = update;
            isUpdate = update.ToLower() == "updatemessage";
            log_path = WarnerImport.LOG_LOCATION +
                Util.dirname(xmlPath).Replace("\\", "/").Replace(WarnerImport.FOLDER_LOCATION, string.Empty) + '/';
            log_success = log_path + "success.dat";
            log_update = log_path + "update.dat";

            if (!Directory.Exists(log_path))
            {
                Directory.CreateDirectory(log_path);
            }
        }

        public void readDealInformation(XmlNode deal)
        {
            XmlNode dealTerms = deal.SelectSingleNode("DealTerms");
            if (dealTerms == null)
            {
                throw new Exception("MetadataMissing");
            }

            startDate = dealTerms.getText("ValidityPeriod/StartDateTime");
            endDate = dealTerms.getText("ValidityPeriod/EndDateTime");

            foreach (XmlNode territory in dealTerms.SelectNodes("TerritoryCode"))
            {
                string _t = territory.getText();
                if (!_t.isEmpty())
                {
                    territoryCode.Add(_t);
                }
            }
        }

        public void getCover(XmlNode res)
        {
            XmlNodeList covers = res.SelectNodes("Image");
            foreach (XmlNode c in covers)
            {
                if (c.getText("ImageType") == "FrontCoverImage")
                {
                    XmlNode file = c.SelectSingleNode("ImageDetailsByTerritory/TechnicalImageDetails/File");

                    if (file == null)
                    {
                        if (!isUpdate)
                        {
                            // not update and no image , error
                            Util.writeToWholeLog("Missing Image Property in MetaData", true);
                            result.Add("[Error] Missing Image Property in MetaData");
                        }
                        break;
                    }

                    Dictionary<string, object> _result = Util.checkFileExist(file, BASE_PATH);

                    string path = (string)_result["path"];
                    if ((bool)_result["success"])
                    {
                        COVER_PATH = path;
                        DB_COVER_PATH = path.Replace(WarnerImport.FOLDER_LOCATION, WarnerImport.NAS_DIR);
                        if (Util.uploadFileBySFTP(path, DB_COVER_PATH))
                        {
                            Util.writeToWholeLog("Upload Cover Success");
                        }
                        else
                        {
                            Util.writeToWholeLog("Upload Cover Fail", true);
                            success = false;
                        }
                    }
                    else
                    {
                        success = false;
                        string msg = (string)_result["msg"];

                        Util.writeToWholeLog("Cover fail :" + path +
                            " with reason : " + msg, true);

                        result.Add("[ERROR] " + path + " fail because " + msg);
                    }

                }
            }
        }

        public void getAlbum(XmlNode rel)
        {
            XmlNodeList releases = rel.SelectNodes("Release");
            foreach (XmlNode r in releases)
            {
                if (r.getText("ReleaseReference") == "R0")
                {
                    relType = r.getText("ReleaseType");

                    XmlNode releaseID = r.SelectSingleNode("ReleaseId");
                    grid = releaseID.getText("GRid");
                    icpn = releaseID.getText("ICPN");

                    setRefTitle(r.SelectSingleNode("ReferenceTitle"));

                    foreach (XmlNode detail in r.SelectNodes("ReleaseDetailsByTerritory"))
                    {

                        relDate = detail.getText("ReleaseDate");
                        if (!string.IsNullOrEmpty(relDate))
                        {
                            string[] d = relDate.Split('-');
                            year = int.Parse(d[0]);
                            month = int.Parse(d[1]);
                        }

                        if (string.IsNullOrEmpty(displayArtist))
                        {
                            displayArtist = detail.getText("DisplayArtistName");
                        }

                        setTitle(detail.SelectNodes("Title"));
                    }

                    copyright = r.getText("PLine/PLineText");

                    break;
                }
            }
        }

        public void getTracks(XmlNode dealList, XmlNode relList, XmlNode res)
        {
            int disk_no = 1;
            int track_no = 1;

            // get the Release in ReleaseList, not the main release, to get the the A1, A2 etc
            List<XmlNode> releaseList = new List<XmlNode>();
            // we need the grouping information here, say disc, track no etc
            XmlNodeList releaseDetail = null;

            foreach (XmlNode release in relList.SelectNodes("Release"))
            {
                if (release.getAttr("IsMainRelease") == "true")
                {
                    // we get the grouping from main release
                    releaseDetail = release.SelectNodes("ReleaseDetailsByTerritory");
                }
                else
                {
                    // we get the release node
                    releaseList.Add(release);
                }
            }
            if (releaseList.Count == 0 || releaseDetail == null)
            {
                throw new Exception("MetadataMissing");
            }

            XmlNodeList resGroup = null;
            foreach (XmlNode territory in releaseDetail)
            {
                string _t = territory.getText("TerritoryCode");
                if (_t == "Worldwide" || _t == "HK")
                {
                    resGroup = territory.SelectNodes("ResourceGroup/ResourceGroup");
                    break;
                }
            }

            if (resGroup == null)
            {
                throw new Exception("MetadataMissing");
            }

            // get the soundrecording in resourecList
            XmlNodeList recordings = res.SelectNodes("SoundRecording");
            /*** End of reading nodes ***/

            foreach (XmlNode releaseDeal in dealList.SelectNodes("ReleaseDeal"))
            {
                // currently we should have one deal only
                XmlNode validity = releaseDeal.SelectSingleNode("Deal/DealTerms/ValidityPeriod");
                if (validity == null)
                {
                    throw new Exception("MetadataMissing: Deal/DealTerms/ValidityPeriod");
                }

                string sd = validity.getText("StartDateTime");
                string ed = validity.getText("EndDateTime");

                XmlNodeList dealRef = releaseDeal.SelectNodes("DealReleaseReference");

                string commercialType = releaseDeal.SelectSingleNode("Deal/DealTerms/CommercialModelType").getText();
                List<string> useTypes = new List<string>();

                XmlNodeList ut = releaseDeal.SelectNodes("Deal/DealTerms/Usage/UseType");
                foreach (XmlNode type in ut)
                {
                    useTypes.Add(type.getText());
                }

                if (commercialType.isEmpty() || useTypes.Count == 0)
                {
                    throw new Exception("MetadataMissing: Deal/DealTerms/CommercialModelType || Deal/DealTerms/Usage/UseType");
                }

                //loop the deal reference
                foreach (XmlNode drf in dealRef)
                {
                    string reference = drf.getText();
                    if (reference.isEmpty())
                    {
                        throw new Exception("MetadataMissing: Resource Reference R0/R1 etc");
                    }

                    // the album one, we have read it
                    if (reference == "R0")
                    {
                        continue;
                    }

                    // we have a reference code(R1, R2 etc), iterate the resource list and get the resource
                    Track t = null;
                    // the OriginalReleaseDate globally, which is quit useless, added in 3.8.2
                    String ord = null;

                    foreach (XmlNode _r in releaseList)
                    {
                        if (_r.getText("ReleaseReference") == reference)
                        {
                            // the A1, A2 etc
                            string resRef = _r.getText("ReleaseResourceReferenceList/ReleaseResourceReference");

                            if (resRef.isEmpty())
                            {
                                throw new Exception("MetadataMissing: Release Resource Reference A1/A2 etc");
                            }

                            // get the disk no and track no from group
                            foreach (XmlNode group in resGroup)
                            {
                                // get track number
                                if (!int.TryParse(group.getText("SequenceNumber"), out disk_no))
                                {
                                    throw new Exception("MetadataMissing: Disk number");
                                }

                                foreach (XmlNode item in group.SelectNodes("ResourceGroupContentItem"))
                                {
                                    if (item.getText("ReleaseResourceReference") == resRef)
                                    {
                                        if (!int.TryParse(item.getText("SequenceNumber"), out track_no))
                                        {
                                            throw new Exception("MetadataMissing: Track number");
                                        }
                                    }
                                }

                            }

                            // get the original release date
                            ord = _r.getText("ReleaseDetailsByTerritory/OriginalReleaseDate");

                            // we have the resource ref (A1,A2) and disk no/track no
                            // create track
                            foreach (XmlNode track in recordings)
                            {
                                if (track.getText("ResourceReference") == resRef)
                                {
                                    t = new Track(BASE_PATH, track.getText("SoundRecordingId/ISRC"), this);
                                    t.disc = disk_no;
                                    t.track = track_no;
                                    t.startDate = sd;                                    
                                    t.endDate = ed;

                                    t.setRefTitle(track.SelectSingleNode("ReferenceTitle"));

                                    XmlNode detail = track.SelectSingleNode("SoundRecordingDetailsByTerritory");

                                    t.setTitle(detail.SelectNodes("Title"));
                                    t.setArtist(detail.SelectNodes("DisplayArtist"));
                                    t.setContri(detail.SelectNodes("ResourceContributor"));
                                    t.setLabel(detail.SelectNodes("LabelName"));
                                    t.setCP(detail.getText("PLine/PLineText"));

                                    String _e = detail.getText("ParentalWarningType").ToLower();
                                    t.setExplicit(
                                        _e.Equals("Explicit".ToLower()) ||
                                        _e.Equals("ExplicitContentEdited".ToLower()));

                                    foreach (XmlNode soundDetail in detail.SelectNodes("TechnicalSoundRecordingDetails"))
                                    {
                                        if (soundDetail.getText("IsPreview").ToLower() == "false")
                                        {
                                            t.setSoundFile(detail.SelectSingleNode("TechnicalSoundRecordingDetails"));
                                        }
                                    }

                                    this.tracks.Add(t);
                                }
                            }

                        }
                    }

                    if (t == null)
                    {
                        // we cannot find the resource with the refernce no
                        throw new Exception("MetadataMissing: Track Data");
                    }
                }
            }
        }

        private void setRefTitle(XmlNode t)
        {
            string title = t.getText("TitleText");

            if (!string.IsNullOrEmpty(title))
            {
                formalTitle = new Title(new Name(title, null));
                this.titles.Add(formalTitle);
            }
        }

        private void setTitle(XmlNodeList titles)
        {
            if (formalTitle == null)
            {
                foreach (XmlNode t in titles)
                {
                    if (t.getAttr("TitleType") == "FormalTitle")
                    {

                        Title temp = new Title(new Name(t.getText("TitleText"), t.getNullableAttr("LanguageAndScriptCode")));

                        if (formalTitle == null)
                        {
                            formalTitle = temp;
                        }
                        else
                        {
                            // formal title has been set , check priority
                            if (Util.checkPriority(formalTitle.lang, temp.lang))
                            {
                                formalTitle = temp;
                            }
                        }

                        this.titles.Add(temp);
                        // we do not break because we want to get the formal title with greatest priority
                    }
                }
            }

            foreach (XmlNode t in titles)
            {
                if (t.getAttr("TitleType") != "FormalTitle")
                {
                    Title temp = new Title(new Name(t.getText("TitleText"), t.getNullableAttr("LanguageAndScriptCode")));

                    // if running above still cannot find formal title
                    if (formalTitle == null)
                    {
                        formalTitle = temp;
                    }
                    else
                    {
                        // formal title has been set , check priority
                        // add if the lang code / title not the same
                        if (temp.lang != formalTitle.lang ||
                            temp.name != formalTitle.name)
                        {
                            this.titles.Add(temp);
                        }
                    }
                }
            }

        }

        public void displayAlbum()
        {
            addAlbumToDB();

            string log = log_path + "log.txt";

            result.Add("Album Name: " + formalTitle.name);
            result.Add("MessageType: " + msgType);
            result.Add("Release Type: " + relType);
            result.Add("Release Date: " + relDate);
            result.Add("Total Duration: " + duration);
            result.Add("grid: " + grid);
            result.Add("icpn: " + icpn);
            result.Add("coverPath: " + COVER_PATH);
            result.Add("copyright: " + copyright);
            result.Add(Environment.NewLine);
            result.Add(Environment.NewLine);

            int count = 1;

            if (album_id != -1)
            {
                foreach (Track t in tracks)
                {
                    success = t.addTrackToDB(album_id) && success;
                    result.Add("Track " + count + " : ");
                    result.AddRange(t.displayTrack(album_id));
                }
            } 

            isCompleted = true;
            Util.writeFile(log, result);
        }

        private void addAlbumToDB()
        {
            TimeSpan dur = TimeSpan.Parse("00:00:00");
            // get the album duration by track duration sum
            foreach (Track t in tracks)
            {
                TimeSpan temp ;
                if (TimeSpan.TryParse(t.getDuration(), out temp)){
                    dur = dur.Add(temp);
                }               
            }

            duration = dur.ToString(@"hh\:mm\:ss\.ff");

            Dictionary<string, object> para = new Dictionary<string, object>
            {
                { "`album_name`" , formalTitle.name},
                { "`grid`" , grid},
                { "`icpn`" , icpn},
                { "`domain`", 2 },
            };

            // seperate For update, to avoid updating empty value
            if (!DB_COVER_PATH.isEmpty())
            {
                para.Add("`cover_link`", DB_COVER_PATH);
            }

            if (!relDate.isEmpty())
            {
                para.Add("`releasedate`", relDate);
            }

            if (!duration.isEmpty())
            {
                para.Add("`duration`", duration);
            }

            if (!startDate.isEmpty())
            {
                DateTime sdt = DateTime.Parse(startDate);
                para.Add("`startdate`", sdt.ToString("yyyy-MM-dd"));
            }

            if (!endDate.isEmpty())
            {
                DateTime edt = DateTime.Parse(endDate);
                para.Add("`enddate`", edt.ToString("yyyy-MM-dd"));
            }
            // end of update

            Dictionary<string, object> where = new Dictionary<string, object>
            {
                { "@W1" , icpn},
                { "@W2" , duration}
            };

            lock (_lock)
            {

                string sql = "select album_id from album where icpn = @W1 and duration = @W2";
                DB.shared.select(sql, where, dr =>
                {
                    if (dr.HasRows)
                    {
                        // album exists
                        dr.Read();
                        album_id = long.Parse(dr["album_id"].ToString());

                        where = new Dictionary<string, object>
                       {
                            { "`album_id`" , album_id}
                       };

                        _r = Util.getUpdateSql("`album`", para, where);

                        DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                        Util.writeToWholeLog("Album Updated : " + album_id + ' ' + formalTitle.name);
                    }
                    else
                    {
                        if (isUpdate && duration == "00:00:00.00")
                        {
                            Util.writeToWholeLog("Inserting new album with zero duration for update", true);
                            return;
                        }

                        _r = Util.getInsertSql("`album`", para);

                        dbresult = DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                        if (dbresult["success"] == DB.SUCCESS)
                        {
                            long id = dbresult["inert_row_id"];
                            album_id = id;

                            Util.writeToWholeLog("Album Inserted : " + album_id + ' ' + formalTitle.name);
                        }

                    }
                });

                if (album_id == -1)
                {
                    return;
                }

                // insert the titles
                foreach (Title t in titles)
                {
                    where = new Dictionary<string, object>
                {
                    { "@W1" , t.name },
                    { "@W2" , album_id }
                };

                    sql = "select 1 from `m3_album_name` where album_name = @W1 and album_id = @W2 and `language` is null";

                    if (!string.IsNullOrEmpty(t.lang))
                    {
                        sql = "select 1 from `m3_album_name` where album_name = @W1 and album_id = @W2 and `language` = @W3";
                        where.Add("@W3", t.lang);
                    }

                    DB.shared.select(sql, where, dr =>
                    {

                        if (dr.HasRows)
                        {
                            // album name already exists , do nth
                        }
                        else
                        {
                            para = new Dictionary<string, object>
                           {
                                { "`album_id`" , album_id },
                                { "`album_name`" , t.name },
                                { "`language`" , t.lang }
                           };

                            _r = Util.getInsertSql("`m3_album_name`", para);

                            DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);

                        }
                    });

                }

            }
        }

        public string getResult()
        {
            return '"' + formalTitle.name + "\" by " + displayArtist;
        }

        public void writeSuccess()
        {
            using (System.IO.StreamWriter file =
                    new System.IO.StreamWriter(log_success, true))
            {
                file.WriteLine(String.Empty);
            }
        }

        public void writeUpdate()
        {
            using (System.IO.StreamWriter file =
                    new System.IO.StreamWriter(log_update, true))
            {
                file.WriteLine(String.Empty);
            }
        }

        public bool isAlreadySuccess()
        {
            return File.Exists(log_success);
        }

        public bool isAlreadyUpdate()
        {
            return File.Exists(log_update);
        }

        public bool shouldTakeDown(XmlNode deal)
        {
            foreach (XmlNode d in deal.SelectNodes("ReleaseDeal/Deal"))
            {
                string endDate = d.getNullableText("DealTerms/ValidityPeriod/EndDateTime");
                if (endDate != null)
                {
                    if (DateTime.Parse(endDate) <= DateTime.Now)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public void takeDown()
        {
            lock (_lock)
            {
                Dictionary<string, object> where = new Dictionary<string, object>
                {
                    { "@W1" , icpn},
                    { "@W2" , grid}
                };

                Dictionary<string, object> para = new Dictionary<string, object>
                {
                    { "`active`" , 0}
                };

                string sql = "select album_id from album where icpn = @W1 and grid = @W2";
                DB.shared.select(sql, where, dr =>
                {
                    if (dr.HasRows)
                    {
                        // album exists
                        dr.Read();
                        album_id = long.Parse(dr["album_id"].ToString());

                        where = new Dictionary<string, object>
                        {
                            { "`album_id`" , album_id}
                        };

                        _r = Util.getUpdateSql("`album`", para, where);

                        DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                        Util.writeToWholeLog("Album Taken Down : " + album_id );
                        takeDownSuccess = true;
                    } 

                });
            }
        }

    }
}
