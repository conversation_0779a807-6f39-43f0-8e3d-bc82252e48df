﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WarnerImport.Class
{
    class Name
    {

        public string name = "";
        public string lang = "";
        public bool isForaml = false;

        public Name(string name, string lang, bool isFormal = false)
        {
            this.name = name.Trim().Normalize(NormalizationForm.FormKC).Replace("’", "'");
            this.lang = lang;
        }

        public virtual void display()
        {
            Console.WriteLine(name + " : " + lang);
        }

    }
}
