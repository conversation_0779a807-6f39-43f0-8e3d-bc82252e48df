﻿using WarnerImport.util;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;
using System.Text.RegularExpressions;
using MySql.Data.MySqlClient;
using System.IO;

namespace WarnerImport.Class
{
    class Track
    {
        private static Dictionary<int, string> STATUS = new Dictionary<int, string>
        {
            { 200 , "FILE SUCCESS" },
            { 404 , "FILE NOT FOUND" },
            { 405 , "FILE NOT MD5 CHECK" },
            { 500 , "FILE CORRUPTED" },
            { 501 , "FILE ENCRYPT FAIL" }
        };

        // the db object
        private static Object _lock = new Object();

        // the album this track in
        private Album album;

        private string BASE_PATH;

        public long track_id = -1;

        private string isrc = "";
        private string label = "";
        private long label_id = -1;
        private string copyright = "";
        private int _explicit = 0;

        public int disc = 1;
        public int track = 1;

        private List<Title> titles = new List<Title>();
        private Title formalTitle = null;

        private List<Artist> artists = new List<Artist>();

        private List<string> result = new List<string>();

        // to get the duration
        private Regex durReg = new Regex("[D|d]uration:.((\\d|:|\\.)*)");
        private Regex gainReg = new Regex("[T|t]rack_gain(\\D+[^-\\d])(-?\\d+\\.\\d+)");
        private Regex discReg = new Regex("_(?<disc>\\d{3,})-(?<track>\\d{3,})");
        private Match m;

        // For db
        private Dictionary<string, long> dbresult;
        private List<object> _r;

        private string[] preg_replace_pattern = new string[] { "\\.[^.]+$" };
        private string[] preg_replace_result = new string[] { "." };

        public string startDate = "";
        public string endDate = "";

        private Dictionary<string, object> soundFile = new Dictionary<string, object>
        {
            { "bitrate" , 0 },
            { "sampRate" , "" },
            { "path" , "" },
            { "isExist" , false },
            { "replaygain" , 0 },
            { "duration" , 0 },
            { "status" , CustomException.FILE_NOT_FOUND },
        };

        public Track(string bp, string isrc, Album a)
        {
            this.BASE_PATH = bp;
            this.isrc = isrc;
            this.album = a;
        }

        public void setRefTitle(XmlNode t)
        {
            string title = t.getText("TitleText");
            string sub = t.getNullableText("SubTitle");

            if (!string.IsNullOrEmpty(title))
            {
                formalTitle = new Title(new Name(title, null), sub);
                titles.Add(formalTitle);
            }
        }

        public void setTitle(XmlNodeList _t)
        {
            // we get the title from reference title
            if (formalTitle == null)
            {
                foreach (XmlNode title in _t)
                {
                    if (title.getAttr("TitleType") == "FormalTitle")
                    {
                        Title temp = new Title(new Name(
                                title.getText("TitleText"),
                                title.getNullableAttr("LanguageAndScriptCode")
                            ), title.getNullableText("SubTitle"));

                        if (formalTitle == null)
                        {
                            formalTitle = temp;
                        }
                        else
                        {
                            if (Util.checkPriority(formalTitle.lang, temp.lang))
                            {
                                formalTitle = temp;
                            }
                        }

                        this.titles.Add(temp);
                    }
                }
            }

            foreach (XmlNode title in _t)
            {
                Title temp = new Title(new Name(
                    title.getText("TitleText"),
                    title.getNullableAttr("LanguageAndScriptCode")
                ), title.getNullableText("SubTitle"));

                // if running above still cannot find formal title
                if (formalTitle == null)
                {
                    formalTitle = temp;
                }
                else
                {
                    // formal title has been set , check priority
                    // add if the lang code / title not the same
                    if (temp.lang != formalTitle.lang ||
                        temp.name != formalTitle.name)
                    {
                        this.titles.Add(temp);
                    }
                }
            }

        }

        public void setArtist(XmlNodeList da)
        {
            foreach (XmlNode artist in da)
            {

                Artist a = new Artist(album);

                foreach (XmlNode name in artist.SelectNodes("PartyName"))
                {
                    a.setName(name.getText("FullName"), name.getNullableAttr("LanguageAndScriptCode"));
                }

                foreach (XmlNode role in artist.SelectNodes("ArtistRole"))
                {
                    a.setRole(role);
                }

                // artist may have their party id
                string id = artist.getNullableText("PartyId");
                if (id != null)
                {
                    a.partyId = long.Parse(id);
                }

                artists.Add(a);

            }
        }

        public void setContri(XmlNodeList rc)
        {
            foreach (XmlNode contri in rc)
            {
                bool found = false;
                XmlNodeList name = contri.SelectNodes("PartyName");
                XmlNodeList role = contri.SelectNodes("ResourceContributorRole");

                foreach (Artist a in artists)
                {
                    foreach (XmlNode pn in name)
                    {
                        if (a.checkNameExist(pn.getText("FullName"), pn.getNullableAttr("LanguageAndScriptCode")))
                        {
                            found = true;
                            break;
                        }
                    }

                    if (found)
                    {
                        foreach (XmlNode pn in name)
                        {
                            a.setName(pn.getText("FullName"), pn.getNullableAttr("LanguageAndScriptCode"));
                        }

                        foreach (XmlNode rcr in role)
                        {
                            a.setRole(rcr);
                        }

                        break;
                    }
                }

                if (!found)
                {
                    Artist a = new Artist(album);

                    foreach (XmlNode pn in name)
                    {
                        a.setName(pn.getText("FullName"), pn.getNullableAttr("LanguageAndScriptCode"));
                    }

                    foreach (XmlNode rcr in role)
                    {
                        a.setRole(rcr);
                    }

                    this.artists.Add(a);
                }

            }
        }

        public void setLabel(XmlNodeList labels)
        {
            // just get the first label
            foreach (XmlNode label in labels)
            {
                this.label = label.getText();
                break;
            }

            lock (_lock)
            {
                foreach (KeyValuePair<long, string> lb in WarnerImport.label)
                {
                    if (lb.Value == this.label)
                    {
                        label_id = lb.Key;
                        return;
                    }
                }

                // new label , add to db
                string query = "insert into `label` (`label_name` , `brand_id`) " +
                    "values (@LABEL , @BRAND)";
                Dictionary<string, object> para = new Dictionary<string, object>
                {
                    { "@LABEL" , this.label },
                    { "@BRAND" , 3 } // ** For Warner Label
                };

                Dictionary<string, long> result = DB.shared.query(query, para);
                if (result["success"] == DB.SUCCESS)
                {
                    long id = result["inert_row_id"];
                    label_id = id;
                    WarnerImport.label.Add(id, this.label);
                }

            }

        }

        public void setCP(string cp)
        {
            this.copyright = cp;
        }

        public void setExplicit(bool explcit)
        {
            _explicit = explcit ? 1 : 0;
        }

        public void setSoundFile(XmlNode detail)
        {
            soundFile["bitrate"] = detail.getInt("BitRate");
            XmlNode samp = detail.SelectSingleNode("SamplingRate");
            soundFile["sampRate"] = samp.getText() + samp.getAttr("UnitOfMeasure");

            XmlNode file = detail.SelectSingleNode("File");
            if (file == null)
            {
                if (!album.isUpdate)
                {
                    Util.writeToWholeLog("Missing MusicFile Property in MetaData", true);
                    result.Add("[ERROR] Missing MusicFile Property in MetaData");
                }
            }

            Dictionary<string, object> _result = Util.checkFileExist(file, BASE_PATH);

            if (album.isUpdate && (int)_result["status"] == CustomException.FILE_NOT_FOUND)
            {
                _result["status"] = CustomException.UPDATE_BUT_FILE_NOT_FOUND;
            }

            soundFile["path"] = (string)_result["path"];
            soundFile["status"] = _result["status"];

            if ((bool)_result["success"])
            {
                // get duration
                string result = Util.execute("-i \"" + soundFile["path"] + "\"");
                m = durReg.Match(result);

                if (m.Success)
                {
                    //Means the output has cantained the string "Duration"
                    soundFile["duration"] = m.Groups[1].Value;
                }

                // get replay gain
                result = Util.execute("-i \"" + soundFile["path"] + "\" -af \"replaygain\" -f null /dev/null ");
                m = gainReg.Match(result);

                if (m.Success)
                {
                    //Means the output has cantained the string "track_gain"
                    soundFile["replaygain"] = double.Parse(m.Groups[2].Value ?? "0") * 100;
                }
            }
            else
            {
                string msg = (string)_result["msg"];

                Util.writeToWholeLog("Sound File fail :" + soundFile["path"] +
                    " with reason : " + msg, true);

                result.Add("[ERROR] " + soundFile["path"] + " fail because " + msg);
            }

        }

        public bool addTrackToDB(long album_id)
        {
            int _status = (int)soundFile["status"];
            // album update may have 2 circumstances
            // 1: no track file update, ie: the track will not be found, but it is ok
            // 2: track needs to be update, but it is not found. this is not ok
            if ( _status != CustomException.FILE_SUCCESS && !album.isUpdate ||
                _status == CustomException.UPDATE_BUT_FILE_NOT_FOUND )
            {
                Util.writeToWholeLog("Sound Track File Error : " + _status + ' ' + Track.STATUS[_status]);
                Util.writeToWholeLog("Track File <" + formalTitle.name + "> (" +
                    (string)soundFile["path"] + ") did not add to DB", true);
                return false;
            }

            string title = formalTitle.name + (formalTitle.sub == null ? "" : " (" + formalTitle.sub + ')');

            Dictionary<string, object> para = new Dictionary<string, object>
            {
                {"`isrc`" , isrc },
                {"`discno`" , disc },
                {"`trackno`" , track },
                {"`trackname`" , title },
                {"`releasetype`" , album.relType },
                {"`year`" , album.year },
                {"`month`" , album.month },
                {"`copyright`" , copyright },
                {"`explicit`" , _explicit },
                {"`dlanguage`" , formalTitle.lang },
                {"`domain`", 2 } // default domain index
            };

            if (!startDate.isEmpty())
            {
                DateTime sdt = DateTime.Parse(startDate);
                para.Add("`startdate`", sdt.ToString("yyyy-MM-dd"));
            }

            if (!endDate.isEmpty())
            {
                DateTime edt = DateTime.Parse(endDate);
                para.Add("`enddate`", edt.ToString("yyyy-MM-dd"));
            }

            bool fail = false;

            // seperate For update, to avoid updating empty value
            if (_status == CustomException.FILE_SUCCESS)
            {
                para.Add("`duration`", soundFile["duration"]);
                para.Add("`bitrate`", soundFile["bitrate"]);
                para.Add("`samplingrate`", soundFile["sampRate"]);
                para.Add("`replaygain`", soundFile["replaygain"]);

                // insert track
                string bin_path = ((string)soundFile["path"]).Replace(WarnerImport.FOLDER_LOCATION, WarnerImport.LOG_LOCATION)
                            .Replace("resources/", string.Empty).PregReplace(preg_replace_pattern, preg_replace_result) + "bin";

                string db_path = bin_path.Replace(WarnerImport.LOG_LOCATION, WarnerImport.NAS_DIR);

                encrypt_track((string)soundFile["path"], bin_path);

                if (File.Exists(bin_path))
                {
                    // encrypt success
                    para.Add("`md5`", Util.getMD5(bin_path));

                    if (Util.uploadFileBySFTP(bin_path, db_path))
                    {
                        para.Add("`filelocation`", db_path);
                        Util.writeToWholeLog("Upload Track Success");
                        File.Delete(bin_path);
                    }
                    else
                    {
                        Util.writeToWholeLog("Upload Track Fail : " + (string)soundFile["path"], true);
                        result.Add("[ERROR] Upload Track Fail : " + (string)soundFile["path"]);
                        File.Delete(bin_path);
                        fail = true;
                    }

                }
                else
                {
                    Util.writeToWholeLog("Sound Encryption fail :" + (string)soundFile["path"], true);
                    result.Add("[ERROR] Sound Encryption fail :" + (string)soundFile["path"]);
                    soundFile["status"] = CustomException.FILE_ENCRYPT_FAIL;
                    fail = true;
                }
            }

            // end of update

            if (fail)
            {
                return false;
            }

            Dictionary<string, object> where = new Dictionary<string, object>
            {
                {"@A1" , isrc },
                {"@A2" , album.album_id }
            };

            lock (_lock)
            {

                string sql = "select t.track_id from track t , m3_album_link malk where t.isrc = @A1 " +
                "and malk.album_id = @A2 and malk.track_id = t.track_id";

                DB.shared.select(sql, where, dr =>
                {
                    if (dr.HasRows)
                    {
                        // track exists 
                        dr.Read();
                        track_id = long.Parse(dr["track_id"].ToString());

                        where = new Dictionary<string, object>
                       {
                            { "`track_id`" , track_id}
                       };

                        _r = Util.getUpdateSql("`track`", para, where);

                        DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);

                        if (!album.isUpdate)
                        {
                            DB.shared.query("delete from `m3_artist_link` where `track_id` = " + track_id);
                        }

                        Util.writeToWholeLog("Track Updated : " + track_id + ' ' + title);

                    }
                    else
                    {

                        if (_status != CustomException.FILE_SUCCESS)
                        {
                            Util.writeToWholeLog("Inserting an empty track", true);
                            return;
                        }

                        // insert track
                        _r = Util.getInsertSql("`track`", para);

                        dbresult = DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                        if (dbresult["success"] == DB.SUCCESS)
                        {
                            track_id = dbresult["inert_row_id"];

                            // insert the album link
                            para = new Dictionary<string, object>
                           {
                                { "`track_id`" , track_id },
                                { "`album_id`" , album_id }
                           };

                            _r = Util.getInsertSql("`m3_album_link`", para);
                            dbresult = DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);

                            // insert the label link
                            para = new Dictionary<string, object>
                           {
                                { "`track_id`" , track_id },
                                { "`label_id`" , label_id }
                           };

                            _r = Util.getInsertSql("`label_link`", para);
                            dbresult = DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);

                            Util.writeToWholeLog("Track Inserted : " + track_id + ' ' + title);
                        }
                        else
                        {
                            Util.writeToWholeLog("Track Inserted Failure : " + title + " in album : " + album.BASE_PATH);
                        }
                    }
                });

                if (track_id == -1)
                {
                    return album.isUpdate;
                }

                foreach (Title t in titles)
                {
                    where = new Dictionary<string, object>
                    {
                        { "@W1" , t.name },
                        { "@W2" , track_id }
                    };

                    sql = "select 1 from `track_name` where `track_name` = @W1 and `track_id` = @W2";

                    bool langNull = string.IsNullOrEmpty(t.lang);
                    bool subNull = string.IsNullOrEmpty(t.sub);

                    if (!langNull || !subNull)
                    {
                        if (!langNull && !subNull)
                        {
                            sql += " and `language` = @W3 and `subtitle` = @W4";
                            where.Add("@W3", t.lang);
                            where.Add("@W4", t.sub);
                        }
                        else if (!langNull)
                        {
                            sql += " and `language` = @W3 and `subtitle` is null";
                            where.Add("@W3", t.lang);
                        }
                        else
                        {
                            sql += " and `language` is null and `subtitle` = @W3";
                            where.Add("@W3", t.sub);
                        }

                    }
                    else
                    {
                        sql += " and `language` is null and `subtitle` is null";
                    }

                    DB.shared.select(sql, where, dr =>
                    {
                        if (dr.HasRows)
                        {
                            // album name already exists , do nth
                        }
                        else
                        {
                            para = new Dictionary<string, object>
                           {
                                { "`track_id`" , track_id },
                                { "`track_name`" , t.name },
                                { "`language`" , t.lang },
                                { "`subtitle`" , t.sub }
                           };

                            _r = Util.getInsertSql("`track_name`", para);
                            DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);

                            if (t.name.Contains(' '))
                            {
                                para = new Dictionary<string, object>
                                {
                                    { "`track_id`" , track_id },
                                    { "`track_name`" , t.name.Replace(" ", String.Empty) },
                                    { "`language`" , t.lang },
                                    { "`subtitle`" , t.sub }
                                };

                                _r = Util.getInsertSql("`track_name`", para);
                                DB.shared.query((string)_r[0], (Dictionary<string, object>)_r[1]);
                            }

                        }
                    });

                }
            }
            return true;
        }

        public List<string> displayTrack(long album_id)
        {
            foreach (Title t in titles)
            {
                result.Add(t.name + " (" + t.sub + ") in " + t.lang);
            }

            result.Add("disc : " + disc);
            result.Add("track : " + track);
            result.Add("isrc : " + isrc);
            result.Add("year : " + album.year);
            result.Add("month : " + album.month);
            result.Add("status : " + soundFile["status"].ToString());
            result.Add("duration : " + soundFile["duration"].ToString());
            result.Add("replaygain : " + soundFile["replaygain"].ToString());
            result.Add("bitrate : " + soundFile["bitrate"].ToString());
            result.Add("sampRate : " + soundFile["sampRate"].ToString());
            result.Add("path : " + soundFile["path"].ToString());
            result.Add("label : " + label);
            result.Add("copyright : " + copyright);
            result.Add("explicit : " + _explicit);
            result.Add(Environment.NewLine);

            int count = 1;

            foreach (Artist a in artists)
            {
                result.Add((count++).ToString() + '.');
                result.AddRange(a.displayArtist(track_id));
            }

            result.Add(Environment.NewLine);
            result.Add(Environment.NewLine);
            return result;
        }

        private bool encrypt_track(string source, string bin)
        {
            if (File.Exists(source))
            {
                FileStream fs = File.OpenRead(source);

                using (FileStream target = new FileStream(bin, FileMode.Create))
                {
                    // can up to 630 MB? https://stackoverflow.com/questions/2030847/best-way-to-read-a-large-file-into-a-byte-array-in-c#comment21728761_2030865
                    byte[] original = File.ReadAllBytes(source);
                    fs.Close();

                    int size = original.Length;
                    byte[] encrypted = new byte[size];

                    for (int i = 0; i < size; i++)
                    {
                        encrypted[i] = (byte)~original[i];
                    }

                    target.Write(encrypted, 0, size);
                    target.Flush();
                    target.Close();
                    target.Dispose();

                    return true;
                }
            }

            return false;
        }

        public string getDuration()
        {
            return soundFile["duration"].ToString();
        }

    }
}
