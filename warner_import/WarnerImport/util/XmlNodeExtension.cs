﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace WarnerImport.util
{
    public static class XmlNodeExtension
    {

        public static int getInt(this XmlNode xml, string selector, int def = 0)
        {
            XmlNode _n = xml.SelectSingleNode(selector);
            return checkNodeNull(_n) ? def : int.Parse(_n.InnerText ?? def.ToString());
        }

        public static string getText(this XmlNode xml, string selector = null)
        {
            if (selector == null)
            {
                return checkNodeNull(xml) ? "" : xml.InnerText.empty();
            }

            XmlNode _n = xml.SelectSingleNode(selector);
            return checkNodeNull(_n) ? "" : _n.InnerText.empty();
        }

        public static string getNullableText(this XmlNode xml, string selector)
        {
            XmlNode _n = xml.SelectSingleNode(selector);
            if (checkNodeNull(_n))
            {
                return null;
            }

            string temp = _n.InnerText;
            return string.IsNullOrEmpty(temp) ? null : temp;
        }

        public static string getAttr(this XmlNode xml, string selector)
        {
            XmlAttribute _a = xml.Attributes[selector];
            return checkAttrNull(_a) ? null : _a.Value.empty();
        }

        public static string getNullableAttr(this XmlNode xml, string selector)
        {
            XmlAttribute _a = xml.Attributes[selector];
            if (checkAttrNull(_a))
            {
                return null;
            }

            string temp = _a.Value;
            return string.IsNullOrEmpty(temp) ? null : temp;
        }

        private static bool checkNodeNull(XmlNode node)
        {
            return node == null;
        }

        private static bool checkAttrNull(XmlAttribute attr)
        {
            return attr == null;
        }
    }
}
