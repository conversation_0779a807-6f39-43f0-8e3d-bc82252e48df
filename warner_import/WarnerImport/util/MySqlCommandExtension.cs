﻿using System.Collections.Generic;
using MySql.Data.MySqlClient;

namespace WarnerImport.util
{
    public static class MySqlCommandExtension
    {

        public static void bind(this MySqlCommand cmd, Dictionary<string, object> para = null)
        {
            if (para != null)
            {
                foreach (KeyValuePair<string, object> p in para)
                {
                    // bind parameter to command object
                    cmd.Parameters.AddWithValue(p.Key, p.Value);
                }
            }
        }

    }
}
