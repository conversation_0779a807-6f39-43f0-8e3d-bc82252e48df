﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Xml;
using WarnerImport.Class;
using WarnerImport.util;
using System.Net.Mail;
using System.Diagnostics;

namespace WarnerImport
{
    class WarnerImport
    {

        // read from config
        private static string config = "warner.xml";

        public static string FOLDER_LOCATION;
        public static string LOG_LOCATION;
        public static string NAS_DIR;

        private static string folder_path;
        private static string log_path;
        // the log text
        public static string log_text_file;
        private static string log_text_file_name = "log.txt";
        // the record , which dirs have been done
        public static string log_record_file;
        private static string log_record_file_name = "record.dat";

        // the record array
        // 0 : success
        // 1 : failure
        // 2 : update
        // 3 : missingXML
        // 4: take down
        private static Dictionary<string, List<string>> record;

        // hard code with thread pool size 5
        private static int poolSize = 5;
        private static bool needEmail = true;
        private static bool forceUpdate = false;

        // the music folder dir list
        private static List<string> dirList;

        // stroing the role array
        public static Dictionary<long, String> role;
        // stroing the label array
        public static Dictionary<long, String> label;

        private static List<string> inserted = new List<string>();
        private static string date;

        public static List<long> takeDownLabels = new List<long>();

        static void Main(string[] args)
        {
            if (args.Length != 1)
            {
                Console.WriteLine("Parameters Format Wrong! You should pass a YYYYMMDD as a parameter.");
                return;
            }

            Console.WriteLine("This is a program which automatically reads and uploads Warner Files to HKRIA M3.");
            DateTime current = DateTime.Now;
            Console.WriteLine("All Right Reserverd@" + current.Year);

            readConfig();

            date = args[0];
            folder_path = FOLDER_LOCATION + date + "/";
            log_path = LOG_LOCATION + date + "/";

            // Exit Program if The Sony folder with the date does not exist
            if (!Directory.Exists(folder_path))
            {
                throw new Exception("Directory Not Exists. Program Exits.");
            }

            // Create Log Folder if not exist
            if (!Directory.Exists(log_path))
            {
                Directory.CreateDirectory(log_path);
            }

            log_text_file = log_path + log_text_file_name;
            log_record_file = log_path + log_record_file_name;

            init();

            startJob();

            sendEmail();
            takeDownLabel();
            endImport();
        }


        static void startJob()
        {
            try
            {
                startImport();
            }
            catch (Exception ex)
            {
                Util.writeToWholeLog("Main Error: " + ex.Message, true);
                startJob();
            }
        }

        /*
         * Read the config xml
         * */
        private static void readConfig()
        {
            if (!File.Exists(config))
            {
                Console.WriteLine("Config XML Missing.");
                return;
            }

            XmlDocument doc = new XmlDocument();
            doc.Load(config);

            XmlNode _con = doc.SelectSingleNode("warnerconfig");

            if (_con == null)
            {
                return;
            }

            // for checking the params are not null nor empty
            List<string> check = new List<string>();

            XmlNode setting = _con.SelectSingleNode("app_setting");
            if (setting != null)
            {
                poolSize = setting.getInt("thread", 5);
                needEmail = setting.getText("need_email").ToLower() == "true";
                forceUpdate = setting.getText("force_update").ToLower() == "true";
            }

            XmlNode dbinfo = _con.SelectSingleNode("dbinfo");
            if (dbinfo != null)
            {
                AppCommon.SQLServer = dbinfo.getText("host");
                AppCommon.SQLUser = dbinfo.getText("user");
                AppCommon.SQLPassword = dbinfo.getText("password");
                AppCommon.SQLDB = dbinfo.getText("database");

                check.Add(AppCommon.SQLServer);
                check.Add(AppCommon.SQLUser);
                check.Add(AppCommon.SQLPassword);
                check.Add(AppCommon.SQLDB);
            }
            else
            {
                return;
            }

            XmlNode ftpinfo = _con.SelectSingleNode("ftpinfo");
            if (ftpinfo != null)
            {
                AppCommon.FTPServer = ftpinfo.getText("host");
                AppCommon.FTPUser = ftpinfo.getText("user");
                AppCommon.FTPPassword = ftpinfo.getText("password");

                check.Add(AppCommon.FTPServer);
                check.Add(AppCommon.FTPUser);
                check.Add(AppCommon.FTPPassword);
            }
            else
            {
                return;
            }

            XmlNode source = _con.SelectSingleNode("source");
            if (source != null)
            {
                FOLDER_LOCATION = source.getText("root_folder");
                LOG_LOCATION = source.getText("log_folder");
                NAS_DIR = source.getText("nas_dir");

                check.Add(FOLDER_LOCATION);
                check.Add(LOG_LOCATION);
                check.Add(NAS_DIR);
            }
            else
            {
                return;
            }

            XmlNode email = _con.SelectSingleNode("notice");
            if (email != null)
            {
                AppCommon.SMTPHost = email.getText("mail_host");
                AppCommon.SMTPAdd = email.getText("mail_address");

                check.Add(AppCommon.SMTPHost);
                check.Add(AppCommon.SMTPAdd);
            }
            else
            {
                return;
            }

            // check if any are nullo or empty , ie , missing
            if (!check.All(s => !string.IsNullOrEmpty(s)))
            {
                Console.WriteLine("Some info is missing in Configuration XML.");
                return;
            }

            foreach (XmlNode _t in email.SelectNodes("to/staff"))
            {
                string mail = _t.getText();
                if (string.IsNullOrEmpty(mail))
                {
                    continue;
                }

                AppCommon.SMTPTo.Add(mail);
            }

            XmlNode takeDown = _con.SelectSingleNode("takedown");
            foreach (XmlNode _t in takeDown.SelectNodes("label"))
            {
                string l = _t.getText();
                long _temp = 0;
                if (!long.TryParse(l, out _temp))
                {
                    continue;
                }

                takeDownLabels.Add(_temp);
            }

        }

        /*
         * Read the record.dat or Init it 
        */
        private static void init()
        {
            bool record_inited = false;
            if (File.Exists(log_record_file))
            {
                using (StreamReader r = new StreamReader(log_record_file))
                {
                    string json = r.ReadToEnd();

                    record = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(json);

                    if (record != null)
                    {
                        record_inited = true;
                    }
                }
            }

            if (!record_inited)
            {
                record = new Dictionary<string, List<string>>
                {
                    { "success" , new List<string>() },
                    { "failure" , new List<string>() },
                    { "update" , new List<string>() },
                    { "missingXML" , new List<string>() },
                    { "takeDown" , new List<string>() }
                };
            }

            // get the role
            DB.shared.select("select role_id , role from role order by role_id asc", null, dr =>
            {
                role = new Dictionary<long, string>();
                while (dr.Read())
                {
                    role.Add((int)dr["role_id"], (string)dr["role"]);
                }
            });

            // get the label
            DB.shared.select("select label_id , label_name from label order by label_id asc", null, dr =>
            {
                label = new Dictionary<long, string>();
                while (dr.Read())
                {
                    label.Add((int)dr["label_id"], (string)dr["label_name"]);
                }
            });
        }

        private static void startImport()
        {
            Util.writeToWholeLog("------------ Process Start ------------");

            /**
             * Warner file structure:
             * folder by time(may more than one), then source folder
             * so we need to order the outside first, then inside
             * eg: 20170814214600826/S_054391974198
             * */
            string[] dirs = Directory.GetDirectories(folder_path);

            if (dirs.Length <= 0)
            {
                throw new Exception("No Directories inside the path is found.");
            }

            // to sort the time folder in the order of time first, 20170814214600826
            Array.Sort(dirs, delegate (string str1, string str2)
            {
                return str1.CompareTo(str2);
            });

            dirList = new List<string>();

            foreach (string dir in dirs)
            {
                string[] _ds = Directory.GetDirectories(dir);

                if (_ds.Length <= 0)
                {
                    // if the folder has no files, it is ok
                    continue;
                }

                // sort the inside source folder
                Array.Sort(_ds, delegate (string str1, string str2)
                {
                    /*string t1 = str1.Split('_')[1];
                    string t2 = str2.Split('_')[1];*/
                    return str1.CompareTo(str2);
                });

                dirList.AddRange(_ds);
            }

            int count = dirList.Count();

            if (count <= 0)
            {
                throw new Exception("No Files inside the path is found.");
            }

            for (int i=0; i<count; i++)
            {
                string s = dirList[i].Replace('\\', '/');
                dirList[i] = s;
            } 

            importParallel(dirList);
        }

        private static void importParallel(List<string> dirs)
        {
            Parallel.For(
                0,
                dirs.Count,
                new ParallelOptions { MaxDegreeOfParallelism = poolSize },
                (i) =>
                {
                    readXML(dirs[i]);
                }
            );

            Console.WriteLine("should print this when all finish");
        }

        private static void readXML(string path)
        {
            if (!forceUpdate)
            {
                if (record["success"].Contains(path) 
                    || record["update"].Contains(path)
                    || record["takeDown"].Contains(path) )
                {
                    return;
                }
            }

            var xml = new DirectoryInfo(path).GetFiles("*.xml", SearchOption.TopDirectoryOnly);
            if (xml.Length == 0)
            {
                Util.writeToWholeLog("Missing XML file in " + path, true);
                record["missingXML"].Add(path);
                return;
            }
            else if (xml.Length != 1)
            {
                Util.writeToWholeLog("More than 1 XML file in " + path, true);
                record["missingXML"].Add(path);
                return;
            }

            string xmlPath = xml[0].FullName;

            Util.writeToWholeLog("Going to read xml : " + xmlPath);

            // Start to read the xml
            XmlDocument doc = new XmlDocument();

            try
            {
                doc.Load(xmlPath);
            }
            catch (Exception ex)
            {
                Util.writeToWholeLog("MalFormat XML file in " + path, true);
                record["missingXML"].Add(path);
                return;
            }

            // the message header
            XmlNode header = doc.DocumentElement.SelectSingleNode("MessageHeader");

            // update indicator
            XmlNode update = doc.DocumentElement.SelectSingleNode("UpdateIndicator");

            // resourse list
            XmlNode res = doc.DocumentElement.SelectSingleNode("ResourceList");

            Album am = new Album(path, update.InnerText, xmlPath);

            XmlNode dealList = doc.DocumentElement.SelectSingleNode("DealList");
            foreach (XmlNode relDeal in dealList.SelectNodes("ReleaseDeal"))
            {
                if (relDeal.getText("DealReleaseReference").Equals("R0"))
                {
                    am.readDealInformation(relDeal.SelectSingleNode("Deal"));
                    break;
                }
            }

            XmlNode relList = doc.DocumentElement.SelectSingleNode("ReleaseList");

            // get the album info
            am.getAlbum(relList);

            if (!forceUpdate)
            {
                if (am.isAlreadySuccess())
                {
                    Util.writeToWholeLog("Already Success: " + path, true);
                    record["success"].Add(path);
                    return;
                }
                else if (am.isAlreadyUpdate())
                {
                    Util.writeToWholeLog("Already Check Update (ignored): " + path, true);
                    record["update"].Add(path);
                    return;
                }
            }

            if (am.isUpdate)
            {
                // Take Down
                XmlNode deal = doc.DocumentElement.SelectSingleNode("DealList");

                if (am.shouldTakeDown(deal))
                {
                    am.takeDown();
                    if (am.takeDownSuccess)
                    {
                        record["takeDown"].Add(path);
                        record["failure"].Remove(path);
                        Util.writeToWholeLog("Take Down Success.", true);
                        Util.writeToWholeLog(string.Empty);
                    }
                    else
                    {
                        record["failure"].Add(path);
                        Util.writeToWholeLog("Take Down Fail.", true);
                        Util.writeToWholeLog(string.Empty);
                    }

                    return;
                }

                am.writeUpdate();
                record["update"].Add(path);
               /* Util.writeToWholeLog("Updating Album. Ignored.", true);
                Util.writeToWholeLog(string.Empty);
                return;*/
            }

            // check and upload the cover
            am.getCover(res);

            // get the track info
            am.getTracks(dealList, relList, res);

            // Adding Album and Tracks to Database
            am.displayAlbum();

            if (am.isCompleted && am.success)
            {
                am.writeSuccess();

                record["success"].Add(path);

                if (record["failure"].Contains(path))
                {
                    record["failure"].Remove(path);
                }

                if (record["update"].Contains(path))
                {
                    record["update"].Remove(path);
                }

                inserted.Add(am.getResult());

            }
            /*else if (am.isUpdate)
            {
                if (!record["update"].Contains(path))
                {
                    record["update"].Add(path);
                }
            }*/
            else
            {
                // operation fail
                if (!record["failure"].Contains(path))
                {
                    record["failure"].Add(path);
                }
            }
        }

        private static void takeDownLabel()
        {
            if (takeDownLabels.Count > 0)
            {
                Console.WriteLine("Taking Down Unwanted Labels");

                string sql = "update album set active = 0 where album_id in (" +
                                "select distinct album_id from m3_album_link where track_id in (" +
                                "select track_id from label_link where label_id in (" + 
                                string.Join(",", takeDownLabels) + 
                                ") ) )";

                DB.shared.query(sql);
            }
        }

        private static void endImport()
        {
            Util.writeRecord(record);

            Util.writeToWholeLog("------------ Process End ------------");
            // adding line break
            Util.writeToWholeLog("");

            Console.WriteLine("Log can be read in the log folder.");
            Console.WriteLine("Program finish and exit.");

            keepConsole();
        }

        private static void sendEmail()
        {
            if (!needEmail)
            {
                return;
            }

            MailMessage mail = new MailMessage();
            mail.From = new MailAddress(AppCommon.SMTPAdd);

            foreach (string s in AppCommon.SMTPTo)
            {
                mail.To.Add(s);
            }

            SmtpClient client = new SmtpClient();
            client.Port = 25;
            client.DeliveryMethod = SmtpDeliveryMethod.Network;
            client.UseDefaultCredentials = false;
            client.Host = AppCommon.SMTPHost;
            mail.Subject = "Warner Insert Result on " + date;
            mail.IsBodyHtml = true;

            string body = "Below are the result of warner music insertion in folder: " + folder_path + "<br><br>";
            body += "Success : " + record["success"].Count + "; ";
            body += "Update : " + record["update"].Count + "; ";
            body += "Failure : " + record["failure"].Count + "; ";
            body += "Missing XML : " + record["missingXML"].Count + "<br><br>";

            body += "Below are the new album inserted: <br><br>";
            int count = 1;
            foreach (string s in inserted)
            {
                body += count++.ToString() + " : " + s + "<br>";
            }

            mail.Body = body;
            client.Send(mail);
        }

        [Conditional("DEBUG")]
        private static void keepConsole()
        {
            // Keep the console window open in debug mode.
            Console.ReadKey();
        }

    }
}
