<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<warnerconfig>
	<app_setting>
		<thread>10</thread>
		<need_email>false</need_email>
		<!-- set this to true will ignore the success , and do force update to all files -->
		<force_update>true</force_update>
	</app_setting>
    <dbinfo>
		<!-- the db server host -->
		<host>*************</host>
		<!-- the db use name -->
		<user>sonytest</user>
		<!-- the db user pw -->
		<password>SonyTest2016#</password>
		<!-- the database going to use -->
		<database>sonytest</database>
	</dbinfo>
	<ftpinfo>
		<!-- the ftp server host -->
		<host>*************</host>
		<!-- the ftp use name -->
		<user>swagent</user>
		<!-- the ftp user pw -->
		<password>pR88r4bREfrATrew</password>
	</ftpinfo>
	<source>
		<!-- the song source folder -->
		<root_folder>U:/content/</root_folder>
		<!-- the log root folder -->
		<log_folder>D:/userdata/Warner/</log_folder>
		<!-- the nas dir , omit the '/web/' prefix -->
		<nas_dir>Warner/</nas_dir>
	</source>
	<notice>
		<mail_host>mail.hkria.com</mail_host>
		<mail_address><EMAIL></mail_address>
		<to>
			<staff><EMAIL></staff>
		</to>
	</notice>
</warnerconfig>