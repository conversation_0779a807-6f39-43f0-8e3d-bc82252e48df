﻿using System;
using System.Collections.Generic;
using MySql.Data.MySqlClient;
using System.Data;
using WarnerImport.util;

namespace WarnerImport
{
    class DB
    {
        public static long SUCCESS = 1;
        public static long FAILURE = 0;

        public static DB shared = getInstance();

        private static DB singleTon;

        MySqlConnectionStringBuilder myBuilder;

        private DB()
        {
            myBuilder = new MySqlConnectionStringBuilder();
            myBuilder.Database = util.AppCommon.SQLDB;
            myBuilder.Server = util.AppCommon.SQLServer;
            myBuilder.UserID = util.AppCommon.SQLUser;
            myBuilder.Password = util.AppCommon.SQLPassword;
        }

        private MySqlConnection getConn()
        {
            MySqlConnection conn = new MySqlConnection();
            conn.ConnectionString = myBuilder.ConnectionString;

            try
            {
                conn.Open();
            }
            catch (Exception ex)
            {
                Util.writeToWholeLog("SQL Connect ERROR : " + ex.Message);
                throw new Exception("Unable to connect SQL Server");
            }

            return conn;
        }

        private static DB getInstance()
        {
            if (singleTon == null)
            {
                singleTon = new DB();
            }
            return singleTon;
        }

        // we need to lock before the reader is close due to single instance
        public void select(string query, Dictionary<string, object> para = null, Action<MySqlDataReader> fn = null)
        {
            using (var connection = getConn())
            {
                using (MySqlCommand cmd = getCommand(query, connection))
                {

                    cmd.bind(para);

                    using (var reader = cmd.ExecuteReader())
                    {
                        fn(reader);
                    }

                }
            }
        }

        public Dictionary<string, long> query(string query, Dictionary<string, object> para = null)
        {
            Dictionary<string, long> result = new Dictionary<string, long>
            {
                { "success" , FAILURE } ,
                { "inert_row_id" , -1 }
            };

            using (var connection = getConn())
            {
                using (MySqlCommand cmd = getCommand(query, connection))
                {

                    cmd.bind(para);
                    // set the time out to 10 mins
                    cmd.CommandTimeout = 600;

                    try
                    {
                        cmd.ExecuteNonQuery();
                        result["success"] = SUCCESS;
                        result["inert_row_id"] = cmd.LastInsertedId;
                    }
                    catch (Exception ex)
                    {
                        result["success"] = FAILURE;
                        result["inert_row_id"] = -1;
                        Util.writeToWholeLog("DB Query Error : " + ex.Message, true);
                        Util.writeToWholeLog("DB Query Error : " + query, true);
                    }

                    return result;
                }
            }
        }

        private MySqlCommand getCommand(string query, MySqlConnection conn)
        {
            MySqlCommand cmd = new MySqlCommand();
            cmd.CommandText = query;

            cmd.CommandType = CommandType.Text;
            cmd.Connection = conn;

            return cmd;
        }
    }
}
