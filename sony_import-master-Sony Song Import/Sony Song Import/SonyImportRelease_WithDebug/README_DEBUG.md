# Sony Song Import - Debug Version

这个版本包含了详细的调试功能，可以帮助诊断"不支持给定路径的格式"错误。

## 新增功能

### 1. 日志级别控制
在 `sony.xml` 配置文件中添加了 `<log_level>` 设置：

```xml
<app_setting>
    <thread>5</thread>
    <need_email>false</need_email>
    <force_update>false</force_update>
    <log_level>DEBUG</log_level>  <!-- 新增：控制日志详细程度 -->
</app_setting>
```

支持的日志级别：
- `DEBUG` - 显示所有调试信息（最详细）
- `INFO` - 显示一般信息
- `SUCCESS` - 只显示成功操作
- `WARNING` - 显示警告和错误
- `ERROR` - 只显示错误

### 2. 路径调试信息
当遇到路径问题时，程序会输出详细的路径调试信息：
- **DIRNAME DEBUG**: `dirname` 方法调用的详细信息
- **PATH DEBUG**: `Path.GetFullPath` 调用的详细信息
- 原始路径内容和长度
- 路径中的特殊字符检查（ASCII码小于32或大于126的字符）
- 当前工作目录信息
- FileInfo 对象创建过程

### 3. 数据库操作调试
显示详细的数据库连接和查询信息：
- 连接创建和打开过程
- SQL 查询执行步骤
- 参数绑定详情

### 4. 文件上传调试
显示 SFTP 上传的详细过程：
- 系统 SFTP 命令尝试
- SSH.NET 库回退机制
- 连接和上传状态

## 使用方法

1. 将所有文件放在同一目录下
2. 根据需要修改 `sony.xml` 配置文件
3. 运行程序：`"Sony Song Import.exe" YYYYMMDD`
4. 查看控制台输出和日志文件中的详细信息

## 故障排除

### "不支持给定路径的格式"错误
如果遇到此错误，查看日志中的调试信息：
- **DIRNAME DEBUG**: 检查 `dirname` 方法处理的路径
- **PATH DEBUG**: 检查 `Path.GetFullPath` 处理的路径
- 查找特殊字符（ASCII码小于32或大于126）
- 确认路径格式是否符合Windows标准
- 检查路径长度是否超过限制
- 查看当前工作目录是否正确

**常见问题**：
- 路径包含非法字符（如控制字符）
- 路径格式不正确（如混合使用 / 和 \ ）
- 路径过长（超过260字符限制）
- 路径包含Unicode字符但编码不正确

### 配置文件问题
确保 `sony.xml` 文件：
- 编码为 UTF-8
- XML 格式正确
- 所有必需的配置项都已填写

## 注意事项

- DEBUG 模式会产生大量日志输出，建议只在调试时使用
- 生产环境建议使用 `SUCCESS` 或 `INFO` 级别
- 日志文件会保存在 `logs/YYYYMMDD/` 目录下
