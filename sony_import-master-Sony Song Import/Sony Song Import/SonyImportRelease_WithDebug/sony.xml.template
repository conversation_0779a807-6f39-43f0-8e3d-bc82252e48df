<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sonyconfig>
	<app_setting>
		<!-- 线程池大小 -->
		<thread>5</thread>
		<!-- 是否发送邮件通知 (true/false) -->
		<need_email>false</need_email>
		<!-- 是否强制更新已存在的记录 (true/false) -->
		<force_update>false</force_update>
		<!-- 日志级别: DEBUG, INFO, SUCCESS, WARNING, ERROR -->
		<log_level>DEBUG</log_level>
	</app_setting>
    <dbinfo>
		<!-- 数据库服务器地址 -->
		<host>YOUR_DATABASE_HOST</host>
		<!-- 数据库端口 -->
    	<port>3306</port>
		<!-- 数据库用户名 -->
		<user>YOUR_DATABASE_USER</user>
		<!-- 数据库密码 -->
		<password>YOUR_DATABASE_PASSWORD</password>
		<!-- 数据库名称 -->
		<database>YOUR_DATABASE_NAME</database>
	</dbinfo>
	<ftpinfo>
		<!-- SFTP 服务器地址 -->
		<host>YOUR_SFTP_HOST</host>
		<!-- SFTP 用户名 -->
		<user>YOUR_SFTP_USER</user>
		<!-- SFTP 密码 -->
		<password>YOUR_SFTP_PASSWORD</password>
	</ftpinfo>
	<source>
		<!-- Sony 文件源目录 -->
		<root_folder>data/sony/</root_folder>
		<!-- 日志文件目录 -->
		<log_folder>logs/</log_folder>
		<!-- NAS 目录（省略 '/web/' 前缀） -->
		<nas_dir>SONY/</nas_dir>
		<!-- 是否启用并行处理 (true/false) -->
		<enable_parallel>false</enable_parallel>
	</source>
	<notice>
		<!-- 邮件服务器 -->
		<mail_host>YOUR_MAIL_HOST</mail_host>
		<!-- 发送者邮箱 -->
		<mail_address>YOUR_SENDER_EMAIL</mail_address>
		<to>
			<!-- 接收者邮箱列表 -->
			<staff><EMAIL></staff>
			<staff><EMAIL></staff>
		</to>
	</notice>
</sonyconfig>
