# Visual Studio / .NET 项目忽略文件

# 编译输出
bin/
obj/
*.exe
*.dll
*.pdb

# NuGet 包
packages/
*.nupkg

# 用户特定文件
*.user
*.suo
*.userosscache
*.sln.docstates

# 构建结果
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/

# Visual Studio 缓存文件
*.cache
*.tmp
*.log

# 配置文件（包含敏感信息）
sony.xml
sony_debug.xml

# 日志文件
logs/
*.log

# 临时文件
*.tmp
*.temp
*~

# macOS 系统文件
.DS_Store
.AppleDouble
.LSOverride

# IDE 文件
.vscode/
.idea/

# 数据文件（可选，根据需要调整）
data/

# 备份文件
*.bak
*.backup
